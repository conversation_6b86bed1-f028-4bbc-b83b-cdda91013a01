import ky from "ky";
import { AppConfig } from "~/configs/app-config";
import { ProductData } from "~/interfaces/dto";
import { ProductListResponse, ProductResponse } from "~/interfaces/http";
import { getErrorMessage, makeRequestHeaders } from "~/utils/http-util";
import { jsonEncodeOrUndefined } from "~/utils/json-utils";

export interface FetchProductsProps {
	includeCount?: boolean;
	count?: number;
	after?: string;
	before?: string;
	start?: string;
	skip?: number;
	sort?: string;
	sortDesc?: boolean;
	include?: string;
	isActive?: boolean;
	categoryId?: string;
	keyword?: string;
	filters?: Record<string, any>;
}

export async function fetchProducts(
	props?: FetchProductsProps,
): Promise<ProductListResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const includeCount = props?.includeCount ?? false;
	const count = props?.count ?? AppConfig.loadMorePerPage;

	const include =
		props?.include ??
		"images,category,prices,prices.pricingScheme,prices.pricingScheme.currency,inventoryLines";

	let apiUrl = `${baseApiUrl}/products?includeCount=${includeCount}&count=${count}&include=${include}`;

	if (props?.after) {
		apiUrl += `&after=${props.after}`;
	}

	if (props?.before) {
		apiUrl += `&before=${props.before}`;
	}

	if (props?.start) {
		apiUrl += `&start=${props.start}`;
	}

	if (props?.skip) {
		apiUrl += `&skip=${props.skip}`;
	}

	if (props?.sort) {
		apiUrl += `&sort=${props.sort}`;
	}

	if (props?.sortDesc !== undefined) {
		apiUrl += `&sortDesc=${props.sortDesc}`;
	}

	if (props?.isActive) {
		apiUrl += `&filter[isActive]=${props.isActive ? "true" : "false"}`;
	}

	if (props?.categoryId) {
		apiUrl += `&filter[categoryId]=${props.categoryId}`;
	}

	if (props?.keyword) {
		apiUrl += `&filter[smart]=${props.keyword}`;
	}

	if (props?.filters) {
		// Encode to JSON.
		const filters = jsonEncodeOrUndefined(props.filters);

		if (filters) {
			apiUrl += `&filters=${filters}`;
		}
	}

	try {
		return await ky
			.get(apiUrl, {
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<ProductListResponse>();
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function findProduct(id: string): Promise<ProductResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const include = "images,category,prices,inventoryLines";

	try {
		return await ky
			.get(`${baseApiUrl}/products/${id}?include=${include}`, {
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<ProductResponse>();
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
