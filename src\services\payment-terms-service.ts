import ky from "ky";
import { AppConfig } from "~/configs/app-config";
import {
	PaymentTermListResponse,
	PaymentTermResponse,
} from "~/interfaces/http";
import { getErrorMessage, makeRequestHeaders } from "~/utils/http-util";
import { jsonEncodeOrUndefined } from "~/utils/json-utils";

export interface FetchPaymentTermsProps {
	includeCount?: boolean;
	count?: number;
	after?: string;
	before?: string;
	start?: string;
	skip?: number;
	sort?: string;
	sortDesc?: boolean;
	include?: string;
	filters?: Record<string, any>;
}

export async function fetchPaymentTerms(
	props?: FetchPaymentTermsProps,
): Promise<PaymentTermListResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const includeCount = props?.includeCount ?? false;
	const count = props?.count ?? AppConfig.loadMorePerPage;
	const include = props?.include ?? "";

	let apiUrl = `${baseApiUrl}/payment-terms?includeCount=${includeCount}&count=${count}&include=${include}`;

	if (props?.after) {
		apiUrl += `&after=${props.after}`;
	}

	if (props?.before) {
		apiUrl += `&before=${props.before}`;
	}

	if (props?.start) {
		apiUrl += `&start=${props.start}`;
	}

	if (props?.skip) {
		apiUrl += `&skip=${props.skip}`;
	}

	if (props?.sort) {
		apiUrl += `&sort=${props.sort}`;
	}

	if (props?.sortDesc) {
		apiUrl += `&sortDesc=${props.sortDesc}`;
	}

	if (props?.filters) {
		// Encode to JSON.
		const filters = jsonEncodeOrUndefined(props.filters);

		if (filters) {
			apiUrl += `&filters=${filters}`;
		}
	}

	try {
		return await ky
			.get(apiUrl, {
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<PaymentTermListResponse>();
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function findPaymentTerm(
	id: string,
): Promise<PaymentTermResponse> {
	try {
		const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
		const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

		return await ky
			.get(`${baseApiUrl}/payment-terms/${id}`, {
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<PaymentTermResponse>();
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
