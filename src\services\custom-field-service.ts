import ky from "ky";
import { CustomFieldsData } from "~/interfaces/dto";
import { makeRequestHeaders } from "~/utils/http-util";

export interface FetchCustomFieldsProps {
	includeCount?: boolean;
}

export async function fetchCustomFields(
	props?: FetchCustomFieldsProps,
): Promise<CustomFieldsData | null> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const includeCount = props?.includeCount ?? false;

	let apiUrl = `${baseApiUrl}/custom-fields?includeCount=${includeCount}`;

	try {
		const customFields = await ky
			.get(apiUrl, {
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<CustomFieldsData>();

		return customFields;
	} catch (e: unknown) {
		return null;
	}
}
