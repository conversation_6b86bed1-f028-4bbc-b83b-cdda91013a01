import { SalesOrderData } from "~/interfaces/dto";
import { formatDate } from "~/utils/formatting-util";

import truckIcon from "@assets/images/icons/order-truck-icon.png";
import orderTrackingIcon from "@assets/images/icons/order-tracking-icon.png";
import { A } from "@solidjs/router";
import DownloadDocumentsForm from "./DownloadDocumentsForm";

export function OrderItemDataRow(props: {
	salesOrder: SalesOrderData;
	class?: string;
}) {
	return (
		<div
			class={`sales-order-item rounded-md border border-slate-300 ${props.class ?? ""}`}
		>
			<header class="flex flex-wrap justify-between rounded-tl-md rounded-tr-md bg-slate-100 px-4 py-4 text-sm leading-6 text-neutral-700 md:leading-5">
				<div class="w-full sm:w-1/2">
					<ul>
						<li class="flex items-start">
							<div class="w-1/2 xl:basis-1/3">Sales Order</div>
							<div>: {props.salesOrder.orderNumber}</div>
						</li>
						<li class="flex items-start">
							<div class="w-1/2 xl:basis-1/3">Customer Order/PO Number</div>
							<div>: {props.salesOrder.poNumber}</div>
						</li>
					</ul>
				</div>
				<div class="w-full sm:w-1/2">
					<ul>
						<li class="flex items-start">
							<div class="w-1/2 xl:basis-1/3">Order Date</div>
							<div>: {formatDate(props.salesOrder.orderDate)}</div>
						</li>
						<li class="flex items-start">
							<div class="w-1/2 xl:basis-1/3">Requested Ship Date</div>
							<div>: {formatDate(props.salesOrder.requestedShipDate)}</div>
						</li>
					</ul>
				</div>
			</header>
			<div class="flex flex-wrap items-center px-4 py-4">
				<div class="flex w-full items-center justify-center lg:w-1/2">
					<img
						src={truckIcon}
						alt=""
						class="mx-auto mb-4 h-auto max-w-[90%] lg:mx-0 lg:mb-0"
					/>
				</div>
				<div class="block w-full text-center lg:flex lg:w-1/2 lg:flex-wrap lg:items-center lg:justify-start">
					<div class="relative">
						<A
							href={`/dashboard/orders/${props.salesOrder.salesOrderId}/order-tracking`}
							class="mr-auto mb-3 ml-auto flex w-full max-w-[250px] items-center justify-center text-sm sm:max-w-none md:text-base lg:mr-4 lg:mb-0 lg:ml-0 lg:w-auto"
						>
							<img
								src={orderTrackingIcon}
								alt="Order Tracking"
								class="mr-2 h-5 w-5"
							/>
							Order Tracking (Coming Soon)
						</A>
					</div>

					<div>
						<DownloadDocumentsForm salesOrder={props.salesOrder} />
					</div>
				</div>
			</div>
		</div>
	);
}
