import LoadingSpinner, { HeaderSection } from "~/components/components";
import { createResource, Show } from "solid-js";
import { findSalesOrder } from "~/services/sales-order-service";
import { useParams } from "@solidjs/router";

import DownloadDocumentsIcon from "@assets/images/icons/download-documents-icon.png";
import OrderTruckIcon from "@assets/images/icons/order-truck-icon.png";
import DownloadDocumentsForm from "~/components/orders/DownloadDocumentsForm";

async function fetchPageData() {
	const params = useParams();
	return await findSalesOrder(params.id);
}

export default function DownloadDocumentsPage() {
	const [salesOrderResponse] = createResource(fetchPageData);

	return (
		<>
			<div class="orders-page pb-20">
				<div class="relative container">
					<HeaderSection
						iconUrl={DownloadDocumentsIcon}
						title="Download Documents"
						subtitle={salesOrderResponse()?.data?.orderNumber ?? ""}
					/>

					<div class="relative mx-auto mt-7">
						<img src={OrderTruckIcon} alt="" class="mx-auto max-h-24 w-auto" />
					</div>

					<div class="mx-auto mt-7">
						<Show when={salesOrderResponse.loading}>
							<LoadingSpinner />
						</Show>

						<Show when={salesOrderResponse.state === "ready"}>
							<Show when={salesOrderResponse.loading}>
								<LoadingSpinner />
							</Show>

							<Show when={salesOrderResponse.state === "ready"}>
								<DownloadDocumentsForm
									salesOrder={salesOrderResponse()?.data}
									class="mx-auto max-w-[90%] px-2 sm:max-w-2xl sm:px-4 md:px-8"
								/>
							</Show>
						</Show>
					</div>
				</div>
			</div>
		</>
	);
}
