import { UserData } from "~/interfaces/dto";
export {};

declare global {
	interface Window {
		legacyHomeSiteOpts: {
			siteUrl: string;
			baseApiUrl: string;
			ajaxUrl: string;
			apiNonce: string;
			logoutUrl: string;
			currentUser: UserData;
		};
	}

	type MenuItem = {
		text: string;
		url: string;
		children?: MenuItem[];
	};

	type DocumentItem = {
		key: string;
		text: string;
		isCustom?: boolean;
	};
}
