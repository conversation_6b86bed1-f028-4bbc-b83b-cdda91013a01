export const EmailConfig = {
	senderEmail: "<EMAIL>",
	replyToName: "Multay Inventory",
	replyToEmail: "<EMAIL>",
};

export const defaultPOLvalue = "Tanjung Emas, Semarang";

export const allowedEmailsToManageJobOrder = [
	"<EMAIL>",
	"<EMAIL>",
];

export const adminEmails = [
	"<EMAIL>",
	"<EMAIL>",
];

export const AppConfig = {
	loadMorePerPage: 100,
	mainMenu: <MenuItem[]>[
		{
			text: "Dining Tables",
			url: "{siteUrl}/dining-tables/",
			children: [
				{
					text: "Rectangle",
					url: "{siteUrl}/product-category/function/dining-table/rectangle/",
				},
				{
					text: "Round",
					url: "{siteUrl}/product-category/function/dining-table/round/",
				},
				{
					text: "Oval",
					url: "{siteUrl}/product-category/function/dining-table/oval/",
				},
				{
					text: "Square",
					url: "{siteUrl}/product-category/function/dining-table/square/",
				},
				{
					text: "Extensions Type",
					url: "{siteUrl}/extensions-type/",
				},
				{
					text: "Base & Inlay",
					url: "{siteUrl}/base/",
				},
			],
		},
		{
			text: "Collections",
			url: "{siteUrl}/collections/",
		},
		{
			text: "Lookbooks",
			url: "{siteUrl}/lookbook/",
		},
		{
			text: "Finishing",
			url: "{siteUrl}/finishing/",
		},
		{
			text: "Fabrics & Leather",
			url: "{siteUrl}/fabrics/",
		},
		{
			text: "Virtual Showroom",
			url: "{siteUrl}/virtual-showroom/",
		},
	],
	supportedDocuments: <DocumentItem[]>[
		{
			key: "qc-certificate",
			text: "QC Certificate",
			isCustom: true,
		},
		{
			key: "sales-order",
			text: "Sales Order",
		},
		{
			key: "invoice",
			text: "Commercial Invoice",
		},
		{
			key: "packing-list",
			text: "Packing List",
		},
		{
			key: "certificate-of-origin",
			text: "Certificate of Origin",
			isCustom: true,
		},
		{
			key: "bill-of-lading",
			text: "Bill of Lading",
			isCustom: true,
		},
		{
			key: "telex-release",
			text: "Telex Release",
			isCustom: true,
		},
		{
			key: "other-documents",
			text: "Other Documents",
			isCustom: true,
		},
	],
	generatedDocuments: <string[]>["sales-order", "invoice", "packing-list"],
	staticDocuments: <string[]>[
		"qc-certificate",
		"certificate-of-origin",
		"bill-of-lading",
		"telex-release",
		"other-documents",
	],
};
