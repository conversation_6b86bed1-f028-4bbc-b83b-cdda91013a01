import { createResource, Show } from "solid-js";
import { RouteSectionProps, useParams } from "@solidjs/router";
import { findInvoiceForm } from "~/services/document-preview-service";
import LoadingSpinner from "~/components/components";
import PackingListHeader from "~/components/documents/packing-list-document/PackingListHeader";
import PackingListTable from "~/components/documents/packing-list-document/PackingListTable";

async function fetchPageData() {
	const params = useParams();
	return await findInvoiceForm(params.id);
}

export default function PackingListDocument(props: RouteSectionProps) {
	const [packingListFormResponse] = createResource(fetchPageData);

	return (
		<div class="w-[1280px] px-4 py-12 lg:w-auto xl:px-20 print:w-auto print:px-0 print:py-0">
			<Show when={packingListFormResponse.loading}>
				<LoadingSpinner />
			</Show>

			<Show
				when={
					packingListFormResponse()?.success && packingListFormResponse()?.data
				}
				fallback={<div>{packingListFormResponse()?.message}</div>}
			>
				<PackingListHeader
					salesOrder={packingListFormResponse()?.data?.salesOrder}
				/>

				<PackingListTable
					salesOrder={packingListFormResponse()?.data?.salesOrder}
					customProductDataCollection={
						packingListFormResponse()?.data?.extraData
							?.customProductDataCollection
					}
					defaultPaymentTerm={
						packingListFormResponse()?.data?.extraData?.defaultPaymentTerm
					}
					options={packingListFormResponse()?.data?.extraData}
				/>
			</Show>
		</div>
	);
}
