export type NoticeDataType =
	| "success"
	| "error"
	| "failed"
	| "warning"
	| "info"
	| undefined;

export interface NoticeData {
	type: NoticeDataType;
	message: string;
}

export interface AppUrlData {
	baseUrl: string;
	baseApiUrl: string;
	apiUrl?: string;
	currentUrl: string;
	keyword?: string | null;
}

export type MetaQuery = {
	relation: string;
	queries:
		| {
				key: string;
				value: string;
				compare: string;
		  }[]
		| MetaQuery[];
};
