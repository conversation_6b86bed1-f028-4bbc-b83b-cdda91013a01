import ky from "ky";
import { BaseResponse } from "~/interfaces/response";
import { getErrorMessage, makeRequestHeaders } from "~/utils/http-util";

export async function updateCustomerPriceListData(props: {
	pricingSchemeSlug: string;
	issuedDate: string;
}): Promise<BaseResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const apiUrl = `${baseApiUrl}/customer-price-list/${props.pricingSchemeSlug}`;

	try {
		return await ky
			.put(apiUrl, {
				credentials: "include",
				headers: makeRequestHeaders(),
				json: {
					issued_date: props.issuedDate,
				},
			})
			.json<BaseResponse>();
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
