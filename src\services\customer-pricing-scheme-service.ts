import ky from "ky";
import {
	CustomerPricingSchemeListResponse,
	CustomerPricingSchemeResponse,
} from "~/interfaces/http";
import { getErrorMessage, makeRequestHeaders } from "~/utils/http-util";

export async function fetchCustomerPricingSchemes(): Promise<CustomerPricingSchemeListResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const apiUrl = `${baseApiUrl}/page-data/customer-pricing-schemes`;

	try {
		const response = await ky
			.get(apiUrl, {
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<CustomerPricingSchemeListResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function findCustomerPricingSchemeBySlug(
	pricingSchemeSlug: string,
): Promise<CustomerPricingSchemeResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const apiUrl = `${baseApiUrl}/page-data/customer-pricing-schemes/${pricingSchemeSlug}`;

	try {
		const response = await ky
			.get(apiUrl, {
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<CustomerPricingSchemeResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
