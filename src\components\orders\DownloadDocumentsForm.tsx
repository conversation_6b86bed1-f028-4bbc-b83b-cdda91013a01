import { FolderDown, LucideDownload, LucideEye } from "lucide-solid";
import { createSignal, For, Show } from "solid-js";
import { AppConfig } from "~/configs/app-config";
import { SalesOrderData } from "~/interfaces/dto";
import AlertDialog from "../dialogs/AlertDialog";
import { ucFirst } from "~/utils/formatting-util";

export default function DownloadDocumentsForm(props: {
	salesOrder?: SalesOrderData;
	class?: string;
}) {
	const siteOpts = window.legacyHomeSiteOpts;
	const user = siteOpts.currentUser;

	const [documentToStream, setDocumentToStream] = createSignal<
		DocumentItem | undefined
	>(undefined);

	let formRef: HTMLFormElement | undefined;
	let serveMethodFieldRef: HTMLInputElement | undefined;

	async function handleDirectStreamDocument(
		document: DocumentItem,
		serveMethod: "stream" | "download",
	) {
		if (serveMethodFieldRef) {
			serveMethodFieldRef.value = serveMethod;
		}

		setDocumentToStream(document);
		formRef?.submit();
	}

	const [unreadyDocToOpen, setUnreadyDocToOpen] = createSignal<
		string | undefined
	>(undefined);

	function waitingForShipment(key: string) {
		if (key !== "invoice" && key !== "packing-list") return false;

		return !props.salesOrder?.isCompleted;
	}

	return (
		<>
			<form
				ref={formRef}
				action={`${siteOpts.siteUrl}/wp-json/legacyhome/v1/direct-stream-document`}
				method="post"
				class={`${props.class ?? ""} mt-7 block md:flex md:flex-wrap md:items-center md:justify-center`}
				target="_blank"
			>
				<input type="hidden" name="_wpnonce" value={siteOpts.apiNonce} />
				<input type="hidden" name="customer_id" value={user.inflowCustomerId} />
				<input
					type="hidden"
					name="sales_order_id"
					value={props.salesOrder?.salesOrderId ?? ""}
				/>
				<input
					type="hidden"
					name="document_name"
					value={documentToStream()?.key ?? ""}
				/>
				<input
					ref={serveMethodFieldRef}
					type="hidden"
					name="serve_method"
					value="stream"
				/>

				<div class="grid w-full grid-cols-1 gap-x-4 gap-y-0.5 sm:grid-cols-2 md:gap-x-6">
					<For each={AppConfig.supportedDocuments}>
						{(document) => (
							<Show
								when={document.isCustom}
								fallback={
									<a
										href={`/dashboard/orders/${props.salesOrder?.salesOrderId}/${document.key}`}
										target="_blank"
										class={`border-lhGrey hover:bg-lightgreycolor relative mb-3 flex h-9 items-center justify-center overflow-hidden rounded-sm border border-solid bg-white pl-3 text-sm text-neutral-700 transition-colors${waitingForShipment(document.key) ? "cursor-not-allowed opacity-50" : ""}`}
										title={`${waitingForShipment(document.key) ? `Waiting for stuffing` : `Preview ${document.text}`}`}
										onClick={(e) => {
											if (waitingForShipment(document.key)) {
												e.preventDefault();
												setUnreadyDocToOpen(document.text);
											}
										}}
									>
										{document.text}
									</a>
								}
							>
								<button
									type="button"
									class="border-lhGrey hover:bg-lightgreycolor relative mb-3 flex h-9 items-center justify-center overflow-hidden rounded-sm border border-solid bg-white pl-3 text-sm text-neutral-700 transition-colors"
									title={`Preview ${document.text}`}
									onClick={() => {
										handleDirectStreamDocument(document, "stream");
									}}
								>
									{document.text}
								</button>
							</Show>
						)}
					</For>
				</div>
				<div class="mt-3 flex w-full items-center">
					<button class="flex items-end text-sm font-light text-black/70 transition-colors hover:text-blue-700">
						<FolderDown
							size={24}
							class="items-end justify-center hover:scale-110"
						/>
						<span class="ml-1.5">Download All Documents</span>
					</button>
				</div>
			</form>

			<AlertDialog
				isOpen={unreadyDocToOpen() !== undefined}
				title="Not Available Yet"
				description={`${ucFirst(unreadyDocToOpen() ?? "This document")} is not available yet and waiting for stuffing.`}
				closeHandler={() => setUnreadyDocToOpen(undefined)}
			/>
		</>
	);
}
