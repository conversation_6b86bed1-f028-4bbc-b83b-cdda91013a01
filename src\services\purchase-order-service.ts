import ky from "ky";
import { AppConfig } from "~/configs/app-config";
import { PurchaseOrderData } from "~/interfaces/dto";
import {
	PurchaseOrderListResponse,
	PurchaseOrderResponse,
} from "~/interfaces/http";
import { getErrorMessage, makeRequestHeaders } from "~/utils/http-util";
import { jsonEncodeOrUndefined } from "~/utils/json-utils";

export interface FetchPurchaseOrdersProps {
	includeCount?: boolean;
	count?: number;
	after?: string;
	before?: string;
	start?: string;
	skip?: number;
	sort?: string;
	sortDesc?: boolean;
	include?: string;
	keyword?: string;
	filters?: Record<string, any>;
}

export async function fetchPurchaseOrders(
	props?: FetchPurchaseOrdersProps,
): Promise<PurchaseOrderListResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const includeCount = props?.includeCount ?? false;
	const count = props?.count ?? AppConfig.loadMorePerPage;
	const include =
		props?.include ??
		"currency,lines,lines.product,lines.product.images,vendor";

	let apiUrl = `${baseApiUrl}/purchase-orders?includeCount=${includeCount}&count=${count}&include=${include}`;

	if (props?.after) {
		apiUrl += `&after=${props.after}`;
	}

	if (props?.before) {
		apiUrl += `&before=${props.before}`;
	}

	if (props?.start) {
		apiUrl += `&start=${props.start}`;
	}

	if (props?.skip) {
		apiUrl += `&skip=${props.skip}`;
	}

	if (props?.sort) {
		apiUrl += `&sort=${props.sort}`;
	}

	if (props?.sortDesc) {
		apiUrl += `&sortDesc=${props.sortDesc}`;
	}

	if (props?.keyword) {
		apiUrl += `&filter[smart]=${props.keyword}`;
	}

	if (props?.filters) {
		// Encode to JSON.
		const filters = jsonEncodeOrUndefined(props.filters);

		if (filters) {
			apiUrl += `&filters=${filters}`;
		}
	}

	try {
		return await ky
			.get(apiUrl, {
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<PurchaseOrderListResponse>();
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function findPurchaseOrder(
	id: string,
): Promise<PurchaseOrderResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const currentTimestamp = new Date().getTime();

	const include = "currency,lines,lines.product,lines.product.images,vendor";
	const apiUrl = `${baseApiUrl}/purchase-orders/${id}?include=${include}&nocache=${currentTimestamp}`;

	try {
		return await ky
			.get(apiUrl, {
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<PurchaseOrderResponse>();
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
