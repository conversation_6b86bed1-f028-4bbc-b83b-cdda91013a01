export function getErrorMessage(response: unknown): string {
	if (typeof response === "string") {
		return response;
	}

	if (response instanceof Error) {
		return response.message;
	}

	const msg = "Unknown error occurred. Please let us know about this issue.";

	if (typeof response === "object" && response) {
		if ("message" in response && typeof response.message === "string") {
			return response.message;
		}

		if (
			"response" in response &&
			typeof response.response === "object" &&
			response.response &&
			"message" in response.response &&
			typeof response.response.message === "string"
		) {
			return response.response.message;
		}

		if ("errors" in response && Array.isArray(response.errors)) {
			if (response.errors.length > 0) {
				const error = response.errors[0];

				if (typeof error === "object" && error) {
					if (error.detail) {
						return error.detail;
					}

					if (error.title) {
						return error.title;
					}
				}
			}

			return msg;
		}
	}

	return msg;
}

export function makeJsonResponse(
	response: any,
	status: number = 200,
): Response {
	"use server";

	return new Response(
		typeof response === "string" ? response : JSON.stringify(response),
		{
			status,
			headers: {
				"Content-Type": "application/json",
			},
		},
	);
}

export function makeRequestHeaders(
	props?: Record<string, string>,
): Record<string, string> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const isLocalMode = legacyHomeSiteOpts.siteUrl.includes(
		"legacyhomeindonesia.local",
	);

	const headers: Record<string, string> = {};

	if (!isLocalMode) {
		headers["X-WP-Nonce"] = legacyHomeSiteOpts.apiNonce;
	}

	if (props) {
		for (const key in props) {
			headers[key] = props[key];
		}
	}

	return headers;
}
