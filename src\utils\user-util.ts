import {
	ProfileData,
	ProfileUpdateData,
	RegistrationData,
} from "../interfaces/dto";
import { slugify } from "./string-util";

export function isAdmin(profile?: ProfileData): boolean {
	return profile && profile.role === "administrator" ? true : false;
}

export function isTeamMember(profile?: ProfileData): boolean {
	return profile && profile.role === "team-member" ? true : false;
}

export function isCustomer(profile?: ProfileData): boolean {
	return profile && profile.role === "customer" ? true : false;
}

export function makeRegistrationData(formData: FormData): RegistrationData {
	const firstName = String(formData.get("first_name"));
	const lastName = String(formData.get("last_name"));

	let displayName = String(formData.get("display_name"));

	if (!displayName) {
		displayName = firstName + " " + lastName;
	}

	let slug = String(formData.get("slug"));

	if (!slug) {
		slug = slugify(displayName);
	}

	return {
		email: String(formData.get("email")),
		username: String(formData.get("email")),
		slug: slug,
		password: String(formData.get("password")),
		role: String(formData.get("role")),
		firstName: firstName,
		lastName: lastName,
		displayName: displayName,
		inflowCustomerId: String(formData.get("inflow_user_id")),
		phone: String(formData.get("phone")),
	};
}

export function makeUserUpdateData(formData: FormData): ProfileUpdateData {
	const data: Record<string, any> = {};

	data.id = formData.get("id") ? formData.get("id") : "";

	if (formData.get("email")) {
		data.email = formData.get("email");
	}

	if (formData.get("password")) {
		data.password = formData.get("password");
	}

	if (formData.get("role")) {
		data.role = formData.get("role");
	}

	if (formData.get("first_name")) {
		data.firstName = formData.get("first_name");
	}

	if (typeof formData.get("last_name") === "string") {
		data.lastName = formData.get("last_name");
	}

	if (formData.get("inflow_user_id")) {
		data.inflowUserId = formData.get("inflow_user_id");
	}

	if (formData.get("phone")) {
		data.phone = formData.get("phone");
	}

	if (formData.get("address")) {
		data.address = formData.get("address");
	}

	if (formData.get("avatar_url")) {
		data.avatarUrl = formData.get("avatar_url");
	}

	return {
		id: data.id,
		...data,
	};
}

export function makeSupabaseUpdateAttributes(
	props: ProfileUpdateData,
): Record<string, string | number> {
	const data: Record<string, any> = {};
	const userMetaData: Record<string, string> = {};

	if (props.email) {
		data.email = props.email;
	}

	if (props.password) {
		data.password = props.password;
	}

	if (props.role) {
		userMetaData.role = props.role;
	}

	if (props.firstName) {
		userMetaData.first_name = props.firstName;
	}

	if (props.lastName !== undefined) {
		userMetaData.last_name = props.lastName;
	}

	if (props.inflowCustomerId) {
		userMetaData.inflow_user_id = props.inflowCustomerId;
	}

	if (props.phone) {
		data.phone = props.phone;
	}

	if (props.avatarUrl) {
		userMetaData.avatar_url = props.avatarUrl;
	}

	data.user_metadata = userMetaData;

	return data;
}
