import {
	CustomProductData,
	PaymentTermData,
	SalesOrderData,
	SalesOrderExtraData,
} from "~/interfaces/dto";
import {
	formatCurrency,
	formatDiscount,
	formatQuantity,
} from "~/utils/formatting-util";
import DocumentFootNote from "../DocumentFootNote";
import DocumentActionButtons from "../DocumentActionButtons";

import anifTtdSrc from "@assets/images/anif-ttd.png";
import { defaultPOLvalue } from "~/configs/app-config";

export interface InvoiceTableProps {
	salesOrder?: SalesOrderData;
	customProductDataCollection?: Record<string, CustomProductData>;
	defaultPaymentTerm?: PaymentTermData;
	incomeCategoryId?: string;
	options?: SalesOrderExtraData;
}

export default function InvoiceTable(props: InvoiceTableProps) {
	if (!props.salesOrder) return null;

	let totalQuantity = 0;
	let subtotalNettWeight = 0;
	let subtotalGrossWeight = 0;
	let totalNettWeight = 0;
	let totalGrossWeight = 0;
	let totalWoodM3 = 0;

	const productLines = props.salesOrder.lines ?? [];

	const hasAnyDiscount = productLines.some((line) => {
		const discount: number = Number(line.discount.value);

		if (discount > 0) {
			return true;
		}

		return false;
	});

	const cbmFromCustomField = props.salesOrder.customFields?.custom1 ?? "";

	const nettWeightFromCustomField =
		props.salesOrder.customFields?.custom6 ?? "";

	const grossWeightFromCustomField =
		props.salesOrder.customFields?.custom7 ?? "";

	const containerAndSealID = props.salesOrder.customFields?.custom5 ?? "";
	const containerAndSealIDSplits = containerAndSealID.split("/");

	const sealID =
		containerAndSealIDSplits[containerAndSealIDSplits.length - 1] ?? "";

	const container = containerAndSealID.replace(`/${sealID}`, "");

	const woodM3FromCustomField = props.salesOrder.customFields?.custom8 ?? "";

	const blNumber = props.salesOrder.customFields?.custom2 ?? "";
	const pod = props.salesOrder.customFields?.custom3 ?? "";

	const hasAnyCustomProductData =
		props.customProductDataCollection &&
		Object.keys(props.customProductDataCollection).length > 0;

	function formatPrice(amount: string | number): string {
		return formatCurrency({
			amount: amount,
			symbol: props.salesOrder?.currency?.symbol,
			thousandsSeparator: props.salesOrder?.currency?.thousandsSeparator,
			decimalSeparator: props.salesOrder?.currency?.decimalSeparator,
			decimalDigits: props.salesOrder?.currency?.decimalPlaces,
		});
	}

	const firstThSharedClass =
		"flex items-center flex-wrap justify-center border-x border-black p-1 font-medium";
	const thSharedClass =
		"flex items-center flex-wrap justify-center border-r border-black px-1 font-medium";

	const firstTdSharedClass = "border-x border-black p-1 xl:p-2 print:p-0.5 ";
	const tdSharedClass = "border-r border-black p-1 xl:p-2 print:p-0.5 ";

	const remarksValueClassName = "font-light uppercase text-slate-600";

	// For customer download, the document should always be stamped.
	const isStamped = true;

	return (
		<div class="text-xs print:text-[10px]">
			<table class="mt-5 w-full table-fixed border-y border-black text-xs print:text-[10px]">
				<thead class="text-center">
					<tr class="flex w-full flex-wrap items-stretch border-b border-black bg-gray-100">
						<th class={`w-4/12 ${firstThSharedClass}`}>Multay Product</th>
						{hasAnyCustomProductData && (
							<th class={`w-2/12 py-1 ${thSharedClass}`}>Customer Product</th>
						)}
						<th
							class={`${
								hasAnyCustomProductData ? "w-2/12" : "w-4/12"
							} py-1 ${thSharedClass}`}
						>
							Picture *)
						</th>
						<th class={`w-1/12 py-1 ${thSharedClass}`}>Quantity (pcs)</th>
						<th class={`w-1/12 py-1 ${thSharedClass}`}>Unit Price (US $)</th>
						<th class={`w-2/12 ${thSharedClass}`}>
							{hasAnyDiscount && (
								<span class="flex h-full w-1/3 items-center justify-center border-r border-black text-center">
									Discount
								</span>
							)}
							<span class={hasAnyDiscount ? "w-2/3" : "w-full"}>
								Subtotal (US $)
							</span>
						</th>
					</tr>
				</thead>

				<tbody class="text-center">
					{props.salesOrder.lines?.map((line) => {
						const isIncomeProduct =
							line.product?.categoryId === props.incomeCategoryId;

						const quantity = line.quantity.standardQuantity;
						const quantityNumber = Number(quantity);
						totalQuantity += isIncomeProduct ? 0 : quantityNumber;
						const discount = line.discount;
						const unitPriceNumber = Number(line.unitPrice);

						const discountNumber = Number(discount.value);
						const discountAmount = discount.isPercent
							? unitPriceNumber * (discountNumber / 100)
							: discountNumber;
						const priceUnitAfterDiscount = unitPriceNumber - discountAmount;
						const priceAfterDiscount = priceUnitAfterDiscount * quantityNumber;

						const customFields = line.product?.customFields;

						const nettWeight = customFields?.custom1 ?? "";
						const nettWeightNumber = Number(nettWeight);

						subtotalNettWeight += nettWeightNumber;
						totalNettWeight += subtotalNettWeight;

						const grossWeight = customFields?.custom2 ?? "";
						const grossWeightNumber = Number(grossWeight);

						subtotalGrossWeight += grossWeightNumber;
						totalGrossWeight += subtotalGrossWeight;

						const woodM3 = customFields?.custom7 ?? "";
						const totalWoodM3Number = Number(woodM3);
						totalWoodM3 += totalWoodM3Number;

						const customFieldsLabel = line.product?.productCustomFieldLabels;
						const CBM = customFieldsLabel?.custom1 ?? "";

						const customProductData =
							props.customProductDataCollection?.[line.productId];

						const images = line.product?.images;
						const imgUrl =
							images && images?.length ? images[0].largeUrl : undefined;

						return (
							<tr class="font-base flex w-full flex-wrap items-stretch border-b border-black text-gray-700">
								<td class={`w-4/12 ${firstTdSharedClass} text-left`}>
									<span class="uppercase">{line.product?.sku}</span>
									<br />
									<span>{line.product?.name}</span>
								</td>
								{hasAnyCustomProductData && (
									<td class={`w-2/12 ${tdSharedClass} text-left`}>
										{customProductData && (
											<>
												{customProductData.customerProductCode && (
													<>
														<span>{customProductData.customerProductCode}</span>
														<br />
													</>
												)}
												<span>{customProductData.customerProductName}</span>
											</>
										)}
									</td>
								)}
								<td
									class={`${
										hasAnyCustomProductData ? "flex w-2/12" : "flex w-4/12"
									} ${tdSharedClass}`}
									colspan={customProductData ? undefined : 2}
								>
									{imgUrl && (
										<img
											src={imgUrl}
											alt="Product image"
											class={`print:max:w-3/4 m-auto max-h-48 print:max-h-20`}
										/>
									)}
								</td>
								<td
									class={`w-1/12 ${tdSharedClass} ${imgUrl ? "" : "flex items-center justify-center"}`}
								>
									{!isIncomeProduct && formatQuantity(quantity)}
								</td>
								<td
									class={`w-1/12 ${tdSharedClass} ${imgUrl ? "" : "flex items-center justify-center"}`}
								>
									{formatPrice(line.unitPrice)}
								</td>
								<td
									class={`flex w-2/12 ${tdSharedClass} ${imgUrl ? "" : "items-center"}`}
								>
									{/* {formatPrice(line.subTotal)} */}
									{hasAnyDiscount && (
										<div class={`${tdSharedClass} h-full w-1/3`}>
											{discount.value !== "0.00000" && (
												<>
													{formatDiscount(discount.value ?? "")}
													{discount.isPercent && <span>%</span>}
												</>
											)}
										</div>
									)}
									<span class={hasAnyDiscount ? "w-2/3" : "w-full"}>
										{formatPrice(priceAfterDiscount)}
									</span>
								</td>
							</tr>
						);
					})}
				</tbody>

				<tbody class="text-center">
					<tr class="flex w-full flex-wrap items-stretch">
						<td
							class={`w-8/12 border-x border-black p-1 font-medium xl:p-2`}
							colspan={hasAnyCustomProductData ? 3 : 2}
						>
							<span class="m-auto">Total</span>
						</td>

						<td class="w-1/12 border-r border-black p-1 xl:p-2">
							{totalQuantity}
						</td>
						<td class="w-1/12 border-r border-black p-1 xl:p-2">&nbsp;</td>
						<td class={`flex w-2/12 ${tdSharedClass} items-center`}>
							{/* {formatPrice(props.salesOrder.total)} */}
							<span
								class={
									hasAnyDiscount ? "w-1/3 border-r border-black" : "hidden"
								}
							></span>
							<span class={hasAnyDiscount ? "w-2/3" : "w-full"}>
								{formatPrice(props.salesOrder.total)}
							</span>
						</td>
					</tr>
				</tbody>
			</table>

			<DocumentFootNote />

			<section class="mt-4 flex w-full justify-between text-xs print:text-[9px] print:leading-[10px]">
				<div class="w-2/5 print:w-3/6">
					<p class="mb-1 font-medium">Remarks :</p>
					<div class="border border-black p-1 xl:p-2 print:font-light">
						<table class="text-xs print:text-[9px]">
							<tbody>
								<tr class="">
									<td class="min-w-[130px]">Container</td>
									<td class={remarksValueClassName}>
										: {container ? container : "-"}
									</td>
								</tr>

								<tr>
									<td>Seal ID</td>
									<td class={remarksValueClassName}>
										: {sealID ? sealID : "-"}
									</td>
								</tr>

								<tr>
									<td>BL Number</td>
									<td class={remarksValueClassName}>
										: {blNumber ? blNumber : "-"}
									</td>
								</tr>

								<tr>
									<td>Nett Weight (kgs)</td>
									<td class={remarksValueClassName}>
										:{" "}
										{nettWeightFromCustomField
											? nettWeightFromCustomField
											: "-"}
									</td>
								</tr>

								<tr>
									<td>Gross Weight (kgs)</td>
									<td class={remarksValueClassName}>
										:{" "}
										{grossWeightFromCustomField
											? grossWeightFromCustomField
											: "-"}
									</td>
								</tr>

								<tr>
									<td>Total CBM</td>
									<td class={remarksValueClassName}>
										: {cbmFromCustomField ? cbmFromCustomField : "-"}
									</td>
								</tr>

								<tr>
									<td>POL</td>
									<td class={remarksValueClassName}>
										:{" "}
										<div class="inline-flex items-center">
											<span>{props.options?.pol ?? defaultPOLvalue}</span>
										</div>
									</td>
								</tr>

								<tr>
									<td>POD</td>
									<td class={remarksValueClassName}>: {pod ? pod : "-"}</td>
								</tr>

								<tr>
									<td>Total Wood M3</td>
									<td class={remarksValueClassName}>
										: {woodM3FromCustomField ? woodM3FromCustomField : "-"}
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>

				{isStamped && (
					<div class="flex w-3/5 items-center justify-end">
						<img src={anifTtdSrc} class="w-52" alt="Multay Stamp" />
					</div>
				)}
			</section>

			<DocumentActionButtons
				salesOrder={props.salesOrder}
				documentKey="packing-list"
			/>
		</div>
	);
}
