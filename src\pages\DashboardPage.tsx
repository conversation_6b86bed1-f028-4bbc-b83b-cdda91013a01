import { A } from "@solidjs/router";

import avatarUrl from "@assets/images/raw-avatar.png";
import ordersIcon from "@assets/images/icons/orders-icon.png";
import ticketsIcon from "@assets/images/icons/tickets-icon.png";
import statementIcon from "@assets/images/icons/statements-icon.png";
import customerServiceIcon from "@assets/images/icons/customer-service-icon.png";
import { DashboardMenuLink, HeaderSection } from "~/components/components";
import { UserData } from "~/interfaces/dto";
import { LucideLogOut } from "lucide-solid";

export default function DashboardPage() {
	const siteOpts = window.legacyHomeSiteOpts;

	const linkSharedClass =
		"flex rounded-sm border border-solid border-lhGrey bg-white p-4 hover:bg-lightcolor";

	const user: UserData = window.legacyHomeSiteOpts.currentUser;

	return (
		<div class="dashboard-page">
			<div class="container relative">
				<div class="mx-auto w-80 text-center">
					<HeaderSection
						iconUrl={avatarUrl}
						title={`Welcome, ${user.displayName}`}
					/>

					<nav class="mt-7 block px-8">
						<DashboardMenuLink
							href="/dashboard/orders/"
							noMarginTop={true}
							iconUrl={ordersIcon}
							text="Your Orders"
						/>

						<A href="/dashboard/tickets/" class={`mt-3 ${linkSharedClass}`}>
							<img src={ticketsIcon} alt="" class="mr-2 h-6 w-6" />
							Your Tickets
						</A>

						<A href="/dashboard/statement/" class={`mt-3 ${linkSharedClass}`}>
							<img src={statementIcon} alt="" class="mr-2 h-6 w-6" />
							Your Statement
						</A>

						<A
							href="/dashboard/customer-service/"
							class={`mt-3 ${linkSharedClass}`}
						>
							<img src={customerServiceIcon} alt="" class="mr-2 h-6 w-6" />
							Customer Service
						</A>

						<a href={`${siteOpts.logoutUrl}`} class={`mt-3 ${linkSharedClass}`}>
							<span class="mr-2 inline-flex h-6 w-6 items-center justify-center rounded-md bg-lhDangerColor font-semibold text-white">
								<LucideLogOut size={14} />
							</span>
							Logout
						</a>
					</nav>
				</div>
			</div>
		</div>
	);
}
