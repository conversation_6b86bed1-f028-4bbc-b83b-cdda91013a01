import { For } from "solid-js";
import StatementIcon from "../../assets/images/icons/statements-icon.png";

interface Props {
	img: string;
	title: string;
}

function Card(props: Props) {
	return (
		<button class="mx-auto mt-6 flex w-80 text-xl items-center justify-start rounded-md border border-solid border-lhGrey bg-gradient-to-t from-gray-200 to-white py-2 pl-2">
			<div class="rounded-md bg-gradient-to-t from-blue-800 to-blue-500 p-2 text-center">
			 	<img	src={props.img} alt="" class="h-14 w-14" />
			</div>
			<p class="ml-4">{props.title}</p>
		</button>
	);
}

export default function YourStatement() {
	const data = [
		{
			img: StatementIcon,
			title: "Customer Statement",
		},
		{
			img: StatementIcon,
			title: "Payment Allocation",
		},
	];

	return (
		<div>
			<div class="container items-center justify-center text-center">
				<img src={StatementIcon} alt="" class="mx-auto mb-2 mt-5 h-20 w-20" />
				<h1 class="text-xl font-semibold">Wecome,</h1>
				<p class="text-lg">Your Statement</p>
			</div>

			<div class="mx-auto mb-10 mt-16 max-w-sm">
				<For each={data}>
					{(item, index) => <Card img={item.img} title={item.title} />}
				</For>
			</div>
		</div>
	);
}
