import { X } from "lucide-solid";
import { For, JSXElement, Show } from "solid-js";
import { useEscapeKey } from "~/hooks/useEscapeKey";

/**
 * An alert dialog component.
 *
 * @param {Object} props - The component props
 * @param {string} [props.title] - The title of the alert dialog
 * @param {string} [props.description] - The description/content of the alert dialog
 * @param {boolean} [props.isOpen] - Controls the visibility of the dialog
 * @param {boolean} [props.isClosable=true] - Whether the dialog can be closed by the user
 * @param {boolean} [props.closeOnOverlayClick=true] - Whether clicking the backdrop closes the dialog
 * @param {boolean} [props.closeOnEscape=true] - Whether pressing Escape key closes the dialog
 */
export default function AlertDialog(props: {
	title?: string;
	description?: string | JSXElement;
	buttons?: JSXElement[];
	buttonsAlignment?: "start" | "center" | "end";
	isOpen?: boolean;
	isClosable?: boolean;
	closeOnOverlayClick?: boolean;
	closeOnEscape?: boolean;
	closeHandler?: () => void;
}) {
	const dialogId = "alert-dialog";

	function isOpen() {
		return props.isOpen ?? false;
	}

	function isClosable() {
		return props.isClosable ?? true;
	}

	function shouldCloseOnOverlayClick() {
		return props.closeOnOverlayClick ?? true;
	}

	function shouldCloseOnEscape() {
		return props.closeOnEscape ?? true;
	}

	function closeModal() {
		if (!isClosable()) return;
		props.closeHandler?.();
	}

	useEscapeKey(shouldCloseOnEscape() ? closeModal : undefined);

	function getButtonsAlignmentClass() {
		switch (props.buttonsAlignment) {
			case "start":
				return "justify-start";
			case "center":
				return "justify-center";
			default:
				return "justify-end";
		}
	}

	return (
		<Show when={isOpen()}>
			<div
				data-expanded={props.isOpen ? "" : undefined}
				class="dialog-backdrop data-[expanded]:animate-in data-[closed]:animate-out data-[closed]:fade-out-0 data-[expanded]:fade-in-0 pointer-events-auto fixed inset-0 z-[9999] bg-[#ffffffcc] backdrop-blur-xs"
				aria-hidden="true"
				onClick={shouldCloseOnOverlayClick() ? closeModal : undefined}
			/>

			<div
				tabindex="-1"
				data-expanded={props.isOpen ? "" : undefined}
				id={`${dialogId}-content`}
				role="alertdialog"
				aria-modal="true"
				aria-labelledby={props.title ? `${dialogId}-title` : undefined}
				aria-describedby={
					props.description ? `${dialogId}-description` : undefined
				}
				class="dialog alert-dialog data-[expanded]:animate-in data-[closed]:animate-out data-[closed]:fade-out-0 data-[expanded]:fade-in-0 data-[closed]:zoom-out-95 data-[expanded]:zoom-in-95 data-[closed]:slide-out-to-left-1/2 data-[closed]:slide-out-to-top-[48%] data-[expanded]:slide-in-from-left-1/2 data-[expanded]:slide-in-from-top-[48%] pointer-events-auto fixed left-1/2 top-1/2 z-[9999] grid w-full max-w-[95%] -translate-x-1/2 -translate-y-1/2 gap-4 rounded-sm border bg-white p-6 shadow-lg duration-200 sm:max-w-sm sm:rounded-lg md:w-full md:max-w-lg"
			>
				{/* Initial focus trap */}
				<span data-focus-trap="" tabindex="0" class="sr-only" />

				<Show when={props.title}>
					<h2 id={`${dialogId}-title`} class="text-lg font-semibold">
						{props.title}
					</h2>
				</Show>

				<Show when={props.description}>
					<p
						id={`${dialogId}-description`}
						class="text-muted-foreground text-sm"
					>
						{props.description}
					</p>
				</Show>

				<Show when={props.buttons && props.buttons.length > 0}>
					<footer
						class={`relative flex w-full flex-wrap ${getButtonsAlignmentClass()} gap-1.5`}
					>
						<For each={props.buttons}>{(btn) => btn}</For>
					</footer>
				</Show>

				<Show when={isClosable()}>
					<button
						type="button"
						aria-label="Close dialog"
						class="ring-offset-background focus:ring-ring data-[expanded]:bg-accent data-[expanded]:text-muted-foreground absolute right-4 top-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:outline-hidden focus:ring-2 focus:ring-offset-2 disabled:pointer-events-none"
						onClick={closeModal}
					>
						<X class="size-4" />
						<span class="sr-only">Close</span>
					</button>
				</Show>

				{/* Final focus trap */}
				<span data-focus-trap="" tabindex="0" class="sr-only" />
			</div>
		</Show>
	);
}
