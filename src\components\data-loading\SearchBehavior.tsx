import { JSXElement, createSignal, onCleanup, onMount } from "solid-js";

// Number (in milliseconds) that we assume that the user is waiting for search result.
const acceptedWaitingDuration = 750;

// The waitingCounter's interval duration (in milliseconds).
const intervalDuration = 25;

// List of e.key values that will affect the search.
const keyupKeys = [
	"a",
	"b",
	"c",
	"d",
	"e",
	"f",
	"g",
	"h",
	"i",
	"j",
	"k",
	"l",
	"m",
	"n",
	"o",
	"p",
	"q",
	"r",
	"s",
	"t",
	"u",
	"v",
	"w",
	"x",
	"y",
	"z",
	"0",
	"1",
	"2",
	"3",
	"4",
	"5",
	"6",
	"7",
	"8",
	"9",
	" ",
	"spacebar",
	"delete",
	"backspace",
	"-",
	"_",
	"@",
	".",
	"/",
];

export interface SearchBehaviorProps {
	searchField?: HTMLInputElement;
	onSearch: (keyword: string) => void;
	children?: JSXElement;
}

export default function SearchBehavior(props: SearchBehaviorProps) {
	// The interval id of waitingCounter.
	let intervalId = 0;

	async function search() {
		if (!props.searchField) return;

		const keyword = props.searchField.value;
		setSearchValue(keyword);
		props.onSearch(keyword);
	}

	// Number (in milliseconds) of the time after user stops typing.
	const [waitingCounter, setWaitingCounter] = createSignal(0);
	const [searchFieldValue, setSearchValue] = createSignal("");

	function prepareSearch() {
		if (!props.searchField) return;

		setWaitingCounter(waitingCounter() + intervalDuration);

		if (waitingCounter() >= acceptedWaitingDuration) {
			resetWaitingCounter();

			if (props.searchField.value === searchFieldValue()) {
				return;
			}

			search();
		}
	}

	function resetWaitingCounter() {
		window.clearInterval(intervalId);
		setWaitingCounter(0);
	}

	function handleInputEvent(e: Event) {
		if (!props.searchField) return;
		resetWaitingCounter();
		intervalId = window.setInterval(prepareSearch, intervalDuration);
	}

	onMount(() => {
		if (!props.searchField) return;

		props.searchField.addEventListener("input", handleInputEvent);
	});

	onCleanup(() => {
		if (!props.searchField) return;
		props.searchField.removeEventListener("input", handleInputEvent);
	});

	return <>{props.children}</>;
}
