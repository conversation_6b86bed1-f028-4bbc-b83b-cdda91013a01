import { formatQuantity, formatWeight } from "~/utils/formatting-util";
import DocumentFootNote from "../DocumentFootNote";

import anifTtdSrc from "@assets/images/anif-ttd.png";
import DocumentActionButtons from "../DocumentActionButtons";
import { InvoiceTableProps } from "../invoice-document/InvoiceTable";
import { defaultPOLvalue } from "~/configs/app-config";

export default function PackingListTable(props: InvoiceTableProps) {
	if (!props.salesOrder) return null;

	let totalQuantity = 0;
	let totalPackage = 0;
	let subtotalNettWeight = 0;
	let subtotalGrossWeight = 0;
	let totalNettWeight = 0;
	let totalGrossWeight = 0;
	let totalWoodM3 = 0;
	let totalM3 = 0;
	let totalCBM = 0;

	const cbmFromCustomField = props.salesOrder.customFields?.custom1 ?? "";

	const nettWeightFromCustomField =
		props.salesOrder.customFields?.custom6 ?? "";

	const grossWeightFromCustomField =
		props.salesOrder.customFields?.custom7 ?? "";

	const containerAndSealID = props.salesOrder.customFields?.custom5 ?? "";
	const containerAndSealIDSplits = containerAndSealID.split("/");

	const sealID =
		containerAndSealIDSplits[containerAndSealIDSplits.length - 1] ?? "";

	const container = containerAndSealID.replace(`/${sealID}`, "");

	const woodM3FromCustomField = props.salesOrder.customFields?.custom8 ?? "";

	const blNumber = props.salesOrder.customFields?.custom2 ?? "";
	const pod = props.salesOrder.customFields?.custom3 ?? "";

	const hasAnyCustomProductData =
		props.customProductDataCollection &&
		Object.keys(props.customProductDataCollection).length > 0;

	const firstThSharedClass =
		"flex items-center flex-wrap justify-center border-x border-black p-1 font-medium";
	const thSharedClass =
		"flex items-center flex-wrap justify-center border-r border-black p-1 font-medium";

	const firstTdSharedClass = "border-x border-black p-1 print:p-0.5 xl:p-2";
	const tdSharedClass = "border-r border-black p-1 print:p-0.5 xl:p-2";

	const remarksValueClassName = "font-light uppercase text-slate-600";

	// For customer download, the document should always be stamped.
	const isStamped = true;

	return (
		<div class="text-xs print:text-[10px]">
			<table class="mt-5 w-full table-fixed border-y border-black text-xs print:text-[10px]">
				<thead class="text-center">
					<tr class="flex w-full flex-wrap items-stretch border-b border-black bg-gray-100">
						<th class={`w-[20%] ${firstThSharedClass}`}>Multay Product</th>
						{hasAnyCustomProductData && (
							<th class={`w-[16%] ${thSharedClass}`}>Customer Product</th>
						)}
						<th
							class={`${
								hasAnyCustomProductData ? "w-[10%]" : "w-[20%]"
							} ${thSharedClass}`}
						>
							Picture *)
						</th>
						<th class={`${thSharedClass} w-[7%]`}>
							Quantity
							<br />
							(pcs)
						</th>
						<th class={`${thSharedClass} w-[7%]`}>
							Packages
							<br />
							(Carton)
						</th>
						<th class={`${thSharedClass} w-[7%]`}>
							NW
							<br />
							(kgs)
						</th>
						<th class={`${thSharedClass} w-[7%]`}>
							GW
							<br />
							(kgs)
						</th>
						<th class={`${thSharedClass} flex w-[7%]`}>
							Total NW
							<br />
							(kgs)
						</th>
						<th class={`${thSharedClass} w-[7%]`}>
							Total GW
							<br />
							(kgs)
						</th>
						<th
							class={`${
								hasAnyCustomProductData ? "w-[6%]" : "w-[9%]"
							} ${thSharedClass}`}
						>
							M3
						</th>
						<th
							class={`${
								hasAnyCustomProductData ? "w-[6%]" : "w-[9%]"
							} ${thSharedClass}`}
						>
							Total
							<br />
							M3
						</th>
					</tr>
				</thead>

				<tbody class="text-center">
					{props.salesOrder.lines?.map((line) => {
						const sku = line.product?.sku ?? "";

						// Service charge is not being shown in packing list form.
						if (sku.includes("SRV-CHRG")) return;

						const quantity = line.quantity.standardQuantity;
						const quantityNumber = Number(quantity);
						totalQuantity += quantityNumber;

						const unitPriceNumber = Number(line.unitPrice);
						const customFields = line.product?.customFields;

						const nettWeight = customFields?.custom1 ?? "";
						const nettWeightNumber = Number(nettWeight);
						subtotalNettWeight += nettWeightNumber;
						totalNettWeight += nettWeightNumber * quantityNumber;

						const grossWeight = customFields?.custom2 ?? "";
						const grossWeightNumber = Number(grossWeight);
						subtotalGrossWeight += grossWeightNumber;
						totalGrossWeight += grossWeightNumber * quantityNumber;

						const woodM3 = customFields?.custom7 ?? "";
						const totalWoodM3Number = Number(woodM3);
						totalWoodM3 += totalWoodM3Number;

						const customFieldsLabel = line.product?.productCustomFieldLabels;
						const CBM = customFieldsLabel?.custom1 ?? "";

						const m3 = line.product?.customFields?.custom3 ?? "";
						const m3Number = Number(m3);

						const subTotalM3 = m3Number * quantityNumber;
						totalM3 += subTotalM3;
						const subTotal = unitPriceNumber * quantityNumber;

						const packages =
							Number(line.product?.customFields.custom6) * quantityNumber;
						totalPackage += packages;

						const customProductData =
							props.customProductDataCollection?.[line.productId];

						return (
							<tr class="font-base flex w-full flex-wrap items-stretch border-b border-black text-gray-700">
								<td class={`${firstTdSharedClass} w-[20%] text-left`}>
									<span class="text-left uppercase">{line.product?.sku}</span>
									<br />
									<span class="text-left">{line.product?.name}</span>
								</td>

								{hasAnyCustomProductData && (
									<td class={`${tdSharedClass} w-[16%] text-left`}>
										{customProductData && (
											<>
												{customProductData.customerProductName && (
													<>
														<span>{customProductData.customerProductCode}</span>
														<br />
													</>
												)}
												<span>{customProductData.customerProductName}</span>
											</>
										)}
									</td>
								)}

								<td
									class={`${
										hasAnyCustomProductData ? "flex w-[10%]" : "flex w-[20%]"
									} ${tdSharedClass}`}
									colspan={customProductData ? undefined : 2}
								>
									{line.product &&
										line.product.images &&
										line.product.images.length > 0 && (
											<img
												src={line.product.images[0].largeUrl}
												alt="Product image"
												class="print:max-w-3/4 z-[-1] m-auto max-h-48 print:max-h-20"
											/>
										)}
								</td>
								<td class={`${tdSharedClass} w-[7%]`}>
									<p>{formatQuantity(quantity)}</p>
								</td>
								<td class={`${tdSharedClass} w-[7%]`}>
									<p>{formatQuantity(packages)}</p>
								</td>
								<td class={`${tdSharedClass} w-[7%]`}>
									{formatWeight(nettWeight)}
								</td>
								<td class={`${tdSharedClass} w-[7%]`}>
									{formatWeight(grossWeight)}
								</td>
								<td class={`${tdSharedClass} w-[7%]`}>
									{formatWeight(nettWeightNumber * quantityNumber)}
								</td>
								<td class={`${tdSharedClass} w-[7%]`}>
									{formatWeight(grossWeightNumber * quantityNumber)}
								</td>
								<td
									class={`${tdSharedClass} ${
										hasAnyCustomProductData ? "w-[6%]" : "w-[9%]"
									}`}
								>
									{formatWeight(m3)}
								</td>
								<td
									class={`${tdSharedClass} ${
										hasAnyCustomProductData ? "w-[6%]" : "w-[9%]"
									}`}
								>
									{formatWeight(subTotalM3)}
								</td>
							</tr>
						);
					})}
				</tbody>

				<tbody class="text-center">
					<tr class="flex w-full flex-wrap items-stretch">
						<td
							class={`${firstTdSharedClass} text-center font-medium ${hasAnyCustomProductData ? "w-[46%]" : "w-[40%]"}`}
							colspan={hasAnyCustomProductData ? 3 : 2}
						>
							Total
						</td>
						<td class={`${tdSharedClass} w-[7%]`}>{totalQuantity}</td>
						<td class={`${tdSharedClass} w-[7%]`}>
							{formatQuantity(totalPackage)}
						</td>
						<td class={`${tdSharedClass} w-[7%]`}></td>
						<td class={`${tdSharedClass} w-[7%]`}></td>
						<td class={`${tdSharedClass} w-[7%]`}>
							{formatWeight(totalNettWeight)}
						</td>
						<td class={`${tdSharedClass} w-[7%]`}>
							{formatWeight(totalGrossWeight)}
						</td>
						<td
							class={`${tdSharedClass} ${
								hasAnyCustomProductData ? "w-[6%]" : "w-[9%]"
							}`}
						>
							&nbsp;
						</td>
						<td
							class={`${tdSharedClass} ${
								hasAnyCustomProductData ? "w-[6%]" : "w-[9%]"
							}`}
						>
							{formatWeight(totalM3)}
						</td>
					</tr>
				</tbody>
			</table>

			<DocumentFootNote />

			<section class="mt-4 flex w-full justify-between text-xs print:text-[9px] print:leading-[10px]">
				<div class="w-2/5 print:w-3/6">
					<p class="mb-1 font-medium">Remarks :</p>
					<div class="border border-black p-1 xl:p-2 print:font-light">
						<table class="text-xs print:text-[9px]">
							<tbody>
								<tr class="">
									<td class="min-w-[130px]">Container</td>
									<td class={remarksValueClassName}>
										: {container ? container : "-"}
									</td>
								</tr>

								<tr>
									<td>Seal ID</td>
									<td class={remarksValueClassName}>
										: {sealID ? sealID : "-"}
									</td>
								</tr>

								<tr>
									<td>BL Number</td>
									<td class={remarksValueClassName}>
										: {blNumber ? blNumber : "-"}
									</td>
								</tr>

								<tr>
									<td>Nett Weight (kgs)</td>
									<td class={remarksValueClassName}>
										:{" "}
										{nettWeightFromCustomField
											? nettWeightFromCustomField
											: "-"}
									</td>
								</tr>

								<tr>
									<td>Gross Weight (kgs)</td>
									<td class={remarksValueClassName}>
										:{" "}
										{grossWeightFromCustomField
											? grossWeightFromCustomField
											: "-"}
									</td>
								</tr>

								<tr>
									<td>Total CBM</td>
									<td class={remarksValueClassName}>
										: {cbmFromCustomField ? cbmFromCustomField : "-"}
									</td>
								</tr>

								<tr>
									<td>POL</td>
									<td class={remarksValueClassName}>
										:{" "}
										<div class="inline-flex items-center">
											<span>{props.options?.pol ?? defaultPOLvalue}</span>
										</div>
									</td>
								</tr>

								<tr>
									<td>POD</td>
									<td class={remarksValueClassName}>: {pod ? pod : "-"}</td>
								</tr>

								<tr>
									<td>Total Wood M3</td>
									<td class={remarksValueClassName}>
										: {woodM3FromCustomField ? woodM3FromCustomField : "-"}
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>

				{isStamped && (
					<div class="flex w-3/5 items-center justify-end">
						<img src={anifTtdSrc} class="w-52" alt="Multay Stamp" />
					</div>
				)}
			</section>

			<DocumentActionButtons
				salesOrder={props.salesOrder}
				documentKey="packing-list"
			/>
		</div>
	);
}
