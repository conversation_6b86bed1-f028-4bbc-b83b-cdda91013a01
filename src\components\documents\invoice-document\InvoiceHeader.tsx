import { PaymentTermData, SalesOrderData } from "~/interfaces/dto";

import multayLogoSrc from "@assets/images/multay-logo-compact.png";
import svlkIconSrc from "@assets/images/svlk-logo.jpg";
import { formatDate } from "~/utils/formatting-util";

export interface InvoiceHeaderProps {
	salesOrder?: SalesOrderData;
	defaultPaymentTerm?: PaymentTermData;
}

export default function InvoiceHeader(props: InvoiceHeaderProps) {
	if (!props.salesOrder) return null;

	return (
		<div class="w-full items-center justify-center">
			<div class="text-[10px] font-medium print:text-[8px]">
				<section class="flex">
					<div class="flex w-1/3 items-center print:w-auto">
						<img src={multayLogoSrc} alt="" class="w-24" />
						<div class="">
							<h4 class="font-semibold">PT. MULTAY INTERNATIONAL INDONESIA</h4>
							<div class="text-slate-600">
								<p>Jl. Raya Demak - Kudus KM7</p>
								<p><PERSON><PERSON><PERSON><PERSON><PERSON>, Wonosalam, Demak 59571</p>
								<p>Central Java, Indonesia</p>
								<p>email <EMAIL></p>
								<p>phone +62291 4284 309</p>
								<p>www.multay.com</p>
							</div>
						</div>
					</div>

					<img src={svlkIconSrc} alt="" class="mx-auto h-20 w-20 pt-3" />

					<div class="w-1/3">
						<div class="ml-auto w-1/2 print:w-4/5">
							<div class="border-docBlue relative mb-3 flex flex-col items-center justify-center rounded-lg border-4 p-[3px]">
								<div class="border-docBlue text-docBlue relative h-full w-full flex-grow rounded-sm border-2 p-3 text-center font-display text-xl font-bold leading-3 print:text-base print:leading-[1px]">
									Commercial Invoice
								</div>
							</div>

							<div class="mt-1 border border-black p-3 print:p-2">
								<div class="flex">
									<p class="font-light">Order #</p>
									<p class="ml-auto font-semibold">
										{props.salesOrder.orderNumber}
									</p>
								</div>
								<div class="flex">
									<p class="mr-5 font-light">Date</p>
									<p class="ml-auto font-semibold">
										{props.salesOrder.invoicedDate
											? formatDate(props.salesOrder.invoicedDate)
											: ""}
									</p>
								</div>
							</div>
						</div>
					</div>
				</section>
				<section class="mt-7 flex">
					<div class="font-base flex">
						<p class="font-semibold">Billing Address :</p>
						<div class="ml-5 font-medium text-slate-600">
							<p class="font-bold">{props.salesOrder.customer?.name}</p>
							<p>{props.salesOrder.billingAddress.address1}</p>
							<p>{props.salesOrder.billingAddress.address2}</p>
							<p>{props.salesOrder.billingAddress.city}</p>
							<p>{props.salesOrder.billingAddress.country}</p>
							<p>{props.salesOrder.billingAddress.postalCode}</p>
							<p>{props.salesOrder.billingAddress.remarks}</p>
							<p>{props.salesOrder.billingAddress.state}</p>
						</div>
					</div>
					<div class="ml-auto flex">
						<p class="font-semibold">Shipping Address :</p>
						<div class="ml-5 text-slate-600">
							<p class="font-bold">{props.salesOrder.customer?.name}</p>
							<p>{props.salesOrder.shippingAddress.address1}</p>
							<p>{props.salesOrder.shippingAddress.address2}</p>
							<p>{props.salesOrder.shippingAddress.city}</p>
							<p>{props.salesOrder.shippingAddress.country}</p>
							<p>{props.salesOrder.shippingAddress.postalCode}</p>
							<p>{props.salesOrder.shippingAddress.remarks}</p>
							<p>{props.salesOrder.shippingAddress.state}</p>
						</div>
					</div>
				</section>
				<section class="mt-7 grid grid-cols-3">
					<div class="flex">
						<div class="mr-2 font-semibold">
							<p>Contact</p>
							<p>Phone</p>
							<p>Email</p>
						</div>
						<div class="ml-12 text-slate-600">
							<p>{props.salesOrder.contactName}</p>
							<p>{props.salesOrder.phone}</p>
							<p>{props.salesOrder.email}</p>
						</div>
					</div>
				</section>
				<section class="mt-7">
					<div class="grid grid-cols-3 border-l border-t border-black bg-gray-100 text-sm print:text-[10px]">
						<div class="border-r border-black py-1 text-center">PO Number</div>
						<div class="border-r border-black py-1 text-center">Sales Rep</div>
						<div class="border-r border-black py-1 text-center">
							Shipment Terms
						</div>
					</div>
					<div class="grid grid-cols-3 border-y border-l border-black text-sm opacity-75 print:text-[10px] print:leading-3">
						<div class="border-r border-black py-1 text-center">
							{props.salesOrder.poNumber}
						</div>
						<div class="border-r border-black py-1 text-center">
							{props.salesOrder.salesRepTeamMember?.name}
						</div>
						<div class="border-r border-black py-1 text-center">
							{props.salesOrder.paymentTerms && (
								<p class="m-auto">{props.salesOrder.paymentTerms.name}</p>
							)}
						</div>
					</div>
				</section>
			</div>
		</div>
	);
}
