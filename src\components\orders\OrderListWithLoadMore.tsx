import { For, Show, batch, createSignal, onCleanup, onMount } from "solid-js";
import { SalesOrderData } from "~/interfaces/dto";
import { SalesOrderListResponse } from "~/interfaces/http";
import LoadMore from "../data-loading/LoadMore";
import SearchBehavior from "../data-loading/SearchBehavior";
import { useSearchParams } from "@solidjs/router";
import { fetchSalesOrders } from "~/services/sales-order-service";
import { OrderItemDataRow } from "./OrderItemDataRow";
import LoadingSpinner, { NotFound } from "../components";

import truckIcon from "@assets/images/icons/order-truck-icon.png";
import { formatDate } from "~/utils/formatting-util";
import { QuoteListTable } from "./QuoteListTable";

export default function OrderListWithLoadMore(props: {
	customerId: string;
	salesOrders: SalesOrderData[] | undefined;
	searchField?: HTMLInputElement;
	yearSelectField?: HTMLSelectElement;
	onTabSwitch?: (tab: "orders" | "quotes") => void;
}) {
	const [searchParams, setSearchParams] = useSearchParams();

	let lastSalesOrderId: string | undefined = props.salesOrders?.length
		? props.salesOrders[props.salesOrders.length - 1].salesOrderId
		: undefined;

	const [orders, setOrders] = createSignal(
		props.salesOrders
			? props.salesOrders.filter((order) => !order.isQuote)
			: [],
	);

	const [quotes, setQuotes] = createSignal(
		props.salesOrders ? props.salesOrders.filter((order) => order.isQuote) : [],
	);

	const availableTabs: Array<"orders" | "quotes"> = ["orders", "quotes"];

	const [activeTab, setActiveTab] = createSignal<"orders" | "quotes">("orders");

	const [doingLoadMore, setDoingLoadMore] = createSignal<boolean>(false);
	const [loadMoreFinished, setLoadMoreFinished] = createSignal<boolean>(false);

	onMount(() => {
		if (!props.yearSelectField) return;
		props.yearSelectField.addEventListener("change", handleYearSelect);
	});

	onCleanup(() => {
		if (!props.yearSelectField) return;
		props.yearSelectField.removeEventListener("change", handleYearSelect);
	});

	async function handleYearSelect(event: Event) {
		const target = event.target;
		if (!(target instanceof HTMLSelectElement)) return;
		const year = target.value ? target.value : undefined;

		setSearchParams({ year: year });

		batch(() => {
			setLoadMoreFinished(false);
			setDoingLoadMore(true);
			setOrders([]);
			setQuotes([]);
		});

		window.setTimeout(async () => {
			const response = await fetchDataList();

			handleFetchComplete(response);
		}, 10);
	}

	async function handleSearch(keyword: string) {
		setSearchParams({ keyword: keyword });

		batch(() => {
			setLoadMoreFinished(false);
			setDoingLoadMore(true);
			setOrders([]);
			setQuotes([]);
		});

		window.setTimeout(async () => {
			const response = await fetchDataList();

			handleFetchComplete(response);
		}, 10);
	}

	async function handleLoadMore() {
		if (doingLoadMore() || loadMoreFinished()) return;

		setDoingLoadMore(true);

		const response = await fetchDataList();

		handleFetchComplete(response);
	}

	async function fetchDataList() {
		const filters: Record<string, any> = {};

		const keyword = Array.isArray(searchParams.keyword)
			? searchParams.keyword[0]
			: searchParams.keyword;

		filters.customerId = props.customerId;

		if (keyword) {
			filters.orderNumber = keyword;
		}

		const year = Array.isArray(searchParams.year)
			? searchParams.year[0]
			: searchParams.year;

		if (year) {
			filters.orderDate = {
				fromDate: `${year}-01-01`,
				toDate: `${year}-12-31`,
			};
		}

		return await fetchSalesOrders({
			after: lastSalesOrderId,
			filters: filters,
		});
	}

	function handleFetchComplete(response: SalesOrderListResponse): void {
		batch(() => {
			setDoingLoadMore(false);

			if (!response.success) {
				alert(response.message);
				return;
			}

			if (!response?.data?.length) {
				setLoadMoreFinished(true);
				return;
			}

			setOrders(
				orders().concat(response.data.filter((order) => !order.isQuote)),
			);

			setQuotes(
				quotes().concat(response.data.filter((order) => order.isQuote)),
			);

			lastSalesOrderId = response.data[response.data.length - 1].salesOrderId;
		});
	}

	function handleTabMenuClick(key: "orders" | "quotes") {
		setActiveTab(key);
		props.onTabSwitch?.(key);
	}

	let sectionRef: HTMLElement | undefined;

	return (
		<>
			<section ref={sectionRef}>
				<SearchBehavior
					searchField={props.searchField}
					onSearch={handleSearch}
				/>
				<LoadMore contentRef={sectionRef} onLoadMore={handleLoadMore}>
					<div class="mty-tabs">
						<nav class="tab-menu border-b-lightgreycolor relative mb-8 flex w-full items-center justify-start border-b-2 px-4">
							<For each={availableTabs}>
								{(tab) => {
									return (
										<a
											class={`tab-menu-item text-lhGrey relative -bottom-0.5 inline-flex cursor-pointer items-center justify-center border-b-2 px-8 py-2 font-semibold ${activeTab() === tab ? "border-b-lhBlue" : "border-b-transparent"}`}
											onClick={(e) => {
												e.preventDefault();
												handleTabMenuClick(tab);
											}}
										>
											{tab === "orders" ? "Orders" : "Quotes"}
										</a>
									);
								}}
							</For>
						</nav>

						<div class="tab-content">
							<div class="tab-content-item">
								<Show
									when={
										((activeTab() === "orders" && !orders().length) ||
											(activeTab() === "quotes" && !quotes().length)) &&
										!doingLoadMore()
									}
								>
									<NotFound
										image={truckIcon}
										title={`No sales ${activeTab() === "orders" ? "orders" : "quotes"} found.`}
										message={`You don't have any sales ${activeTab() === "orders" ? "order" : "quote"} yet.`}
									/>
								</Show>

								<Show when={activeTab() === "orders"}>
									<For each={orders()}>
										{(salesOrder, index) => {
											return (
												<OrderItemDataRow
													salesOrder={salesOrder}
													class={index() > 0 ? "mt-7" : ""}
												/>
											);
										}}
									</For>
								</Show>

								<Show when={activeTab() === "quotes"}>
									<QuoteListTable quotes={quotes()} />
								</Show>
							</div>
						</div>
					</div>
				</LoadMore>

				<Show when={doingLoadMore()}>
					<LoadingSpinner class="mt-14" />
				</Show>
			</section>
		</>
	);
}
