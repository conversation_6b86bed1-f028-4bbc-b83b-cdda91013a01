import ky from "ky";
import {
	InvoiceFormResponse,
	SalesOrderFormResponse,
} from "~/interfaces/response";
import { getErrorMessage, makeRequestHeaders } from "~/utils/http-util";

export async function findDocumentForm(props: {
	salesOrderId: string;
	documentKey: string;
}): Promise<SalesOrderFormResponse | InvoiceFormResponse> {
	if (props.documentKey === "sales-order") {
		return await findSalesOrderForm(props.salesOrderId);
	}

	return {
		success: false,
		message: "Unsupported document key",
	};
}

export async function findSalesOrderForm(
	salesOrderId: string,
): Promise<SalesOrderFormResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;
	const apiUrl = `${baseApiUrl}/document-forms/${salesOrderId}?document_key=sales-order`;

	try {
		return await ky
			.get(apiUrl, {
				credentials: "include",
				headers: makeRequestHeaders(),
				timeout: false,
				retry: {
					limit: 1,
					methods: ["get"],
				},
			})
			.json<SalesOrderFormResponse>();
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function findInvoiceForm(
	salesOrderId: string,
): Promise<InvoiceFormResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;
	const apiUrl = `${baseApiUrl}/document-forms/${salesOrderId}?document_key=packing-list`;

	try {
		return await ky
			.get(apiUrl, {
				credentials: "include",
				headers: makeRequestHeaders(),
				timeout: false,
				retry: {
					limit: 1,
					methods: ["get"],
				},
			})
			.json<InvoiceFormResponse>();
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
