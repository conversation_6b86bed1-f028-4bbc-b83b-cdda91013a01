import { onMount, onCleanup } from "solid-js";

export type EscapeKeyCallback = (e?: KeyboardEvent) => void;

const escapeHandlers = new Map<object, EscapeKeyCallback>();

const handleKeyDown = (e: KeyboardEvent) => {
	// Only iterate through handlers if it's actually an Escape key.
	if (e.key !== "Escape") return;

	// Prevent creating unnecessary array via forEach.
	for (const callback of escapeHandlers.values()) {
		callback(e);
	}
};

let isInitialized = false;

const initializeGlobalListener = () => {
	if (!isInitialized) {
		document.addEventListener("keydown", handleKeyDown);
		isInitialized = true;
	}
};

export const useEscapeKey = (callback?: EscapeKeyCallback): void => {
	// Make it easier for the consumers to actually use the hook by allowing them to pass in undefined.
	if (!callback) return;

	/**
	 * Memoize the handler key at module level to reduce object creation, or use Symbol().
	 */
	const handlerKey = Object.freeze({});

	onMount(() => {
		initializeGlobalListener();
		escapeHandlers.set(handlerKey, callback);
	});

	onCleanup(() => {
		escapeHandlers.delete(handlerKey);

		if (escapeHandlers.size === 0) {
			document.removeEventListener("keydown", handleKeyDown);
			isInitialized = false;
		}
	});
};
