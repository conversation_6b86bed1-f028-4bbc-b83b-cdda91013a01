import { defineConfig } from "vite";
import solidPlugin from "vite-plugin-solid";
import tailwindcss from "@tailwindcss/vite";
import path from "path";
import devtools from "solid-devtools/vite";

export default defineConfig({
	plugins: [
		/* 
		Uncomment the following line to enable solid-devtools.
		For more info see https://github.com/thetarnav/solid-devtools/tree/main/packages/extension#readme
		*/
		devtools(),
		solidPlugin(),
		tailwindcss(),
	],
	resolve: {
		alias: {
			"~": path.resolve(__dirname, "./src"),
			"@assets": path.resolve(__dirname, "./assets"),
		},
	},
	server: {
		port: 5000,
	},
	build: {
		target: "esnext",
		assetsDir: "customer-app",
		emptyOutDir: true,
	},
});
