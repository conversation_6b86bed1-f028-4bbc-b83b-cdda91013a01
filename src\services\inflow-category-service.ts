import ky from "ky";
import { AppConfig } from "~/configs/app-config";
import { CategoryListResponse, CategoryResponse } from "~/interfaces/http";
import { getErrorMessage, makeRequestHeaders } from "~/utils/http-util";

export async function fetchCategories(props?: {
	includeCount?: boolean;
	count?: number;
	after?: string;
	before?: string;
	start?: string;
	skip?: number;
	sort?: string;
	sortDesc?: boolean;
}): Promise<CategoryListResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const includeCount = props?.includeCount ?? false;
	const count = props?.count ?? AppConfig.loadMorePerPage;

	let apiUrl = `${baseApiUrl}/inflow-categories?includeCount=${includeCount}&count=${count}`;

	if (props?.after) {
		apiUrl += `&after=${props.after}`;
	}

	if (props?.before) {
		apiUrl += `&before=${props.before}`;
	}

	if (props?.start) {
		apiUrl += `&start=${props.start}`;
	}

	if (props?.skip) {
		apiUrl += `&skip=${props.skip}`;
	}

	if (props?.sort) {
		apiUrl += `&sort=${props.sort}`;
	}

	if (props?.sortDesc) {
		apiUrl += `&sortDesc=${props.sortDesc}`;
	}

	try {
		return await ky
			.get(apiUrl, {
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<CategoryListResponse>();
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function findCategory(id: string): Promise<CategoryResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	try {
		return await ky
			.get(`${baseApiUrl}/inflow-categories/${id}`, {
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<CategoryResponse>();
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
