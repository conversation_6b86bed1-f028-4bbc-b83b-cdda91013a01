/* @refresh reload */
import { render } from "solid-js/web";

import "./index.css";
import App from "./App";

if (import.meta.env.DEV) {
	function setupDemo() {
		window.legacyHomeSiteOpts = {
			siteUrl: import.meta.env.VITE_LH_SITE_URL,
			baseApiUrl: import.meta.env.VITE_LH_BASE_API_URL,
			ajaxUrl: import.meta.env.VITE_LH_AJAX_URL,
			apiNonce: import.meta.env.VITE_LH_API_NONCE,
			logoutUrl: import.meta.env.VITE_LH_LOGOUT_URL,
			currentUser: {
				id: import.meta.env.VITE_LH_WP_USER_ID,
				email: import.meta.env.VITE_LH_WP_USER_EMAIL,
				username: import.meta.env.VITE_LH_WP_USER_USERNAME,
				slug: import.meta.env.VITE_LH_WP_USER_SLUG,
				firstName: import.meta.env.VITE_LH_WP_USER_FIRST_NAME,
				lastName: import.meta.env.VITE_LH_WP_USER_LAST_NAME,
				displayName: import.meta.env.VITE_LH_WP_USER_DISPLAY_NAME,
				companyName: import.meta.env.VITE_LH_WP_USER_COMPANY_NAME,
				inflowCustomerId: import.meta.env.VITE_LH_WP_USER_INFLOW_CUSTOMER_ID,
				website: import.meta.env.VITE_LH_WP_USER_WEBSITE,
				roles: [import.meta.env.VITE_LH_WP_USER_ROLE],
				role: import.meta.env.VITE_LH_WP_USER_ROLE,
				phone: import.meta.env.VITE_LH_WP_USER_PHONE,
				access: import.meta.env.VITE_LH_WP_USER_ACCESS,
			},
		};
	}

	setupDemo();
}

const root = document.getElementById("root");
if (root) render(() => <App />, root);
