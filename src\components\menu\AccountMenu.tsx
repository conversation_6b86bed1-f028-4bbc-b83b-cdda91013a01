import { A } from "@solidjs/router";

import avatarUrl from "@assets/images/raw-avatar.png";
import ordersIcon from "@assets/images/icons/orders-icon.png";
import ticketsIcon from "@assets/images/icons/tickets-icon.png";
import statementIcon from "@assets/images/icons/statements-icon.png";
import customerServiceIcon from "@assets/images/icons/customer-service-icon.png";
import { AccountMenuLink, HeaderSection } from "~/components/components";
import { UserData } from "~/interfaces/dto";
import { LucideChevronDown, LucideLogOut, LucideUser } from "lucide-solid";

export default function AccountMenu() {
	const siteOpts = window.legacyHomeSiteOpts;
	const currentUser = siteOpts.currentUser;

	const fullName = currentUser.displayName;

	// This `initialName` is the first letter of each user's display name.
	const initialName = fullName
		.split(" ")
		.map((word) => word[0])
		.join("")
		.toUpperCase();

	const linkSharedClass =
		"flex rounded-sm border border-black/20 border-lhGrey bg-white px-4 py-1.5 items-center text-black/70 hover:bg-lightcolor";

	const user: UserData = window.legacyHomeSiteOpts.currentUser;

	return (
		<div class="group relative">
			<button
				type="button"
				class="bg-lhBlue relative inline-flex size-9 items-center justify-center rounded-full px-3 py-1 text-white lg:hidden"
			>
				<span class="text-sm font-semibold">{initialName}</span>
			</button>

			<button
				type="button"
				class="relative hidden truncate text-wrap break-all rounded-md px-3 py-1 text-left font-display text-lhGrey lg:block"
			>
				<span class="block text-sm font-light">
					Hello, {currentUser.firstName}
				</span>
				<span class="flex w-full items-center text-lg">
					Account
					<LucideChevronDown size={16} class="relative left-1 top-[2px]" />
				</span>
			</button>

			<div class="absolute -right-0 top-full -z-10 rounded-md bg-white text-sm opacity-0 shadow-lg group-hover:z-20 group-hover:opacity-100">
				<div class="dashboard-page rounded-xl border border-gray-300 bg-white p-4 shadow-lg">
					<div class="container relative">
						<div class="mx-auto w-60 text-center">
							<HeaderSection
								iconUrl={avatarUrl}
								title={`Welcome, ${user.displayName}`}
							/>

							<nav class="mt-4 block">
								<AccountMenuLink
									href="/dashboard/orders/"
									noMarginTop={true}
									iconUrl={ordersIcon}
									text="Your Orders"
								/>

								<A href="/dashboard/tickets/" class={`mt-3 ${linkSharedClass}`}>
									<img src={ticketsIcon} alt="" class="mr-2 h-6 w-6" />
									Your Tickets
								</A>

								<A
									href="/dashboard/statement/"
									class={`mt-3 ${linkSharedClass}`}
								>
									<img src={statementIcon} alt="" class="mr-2 h-6 w-6" />
									Your Statement
								</A>

								<A
									href="/dashboard/customer-service/"
									class={`mt-3 ${linkSharedClass}`}
								>
									<img src={customerServiceIcon} alt="" class="mr-2 h-6 w-6" />
									Customer Service
								</A>
							</nav>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
