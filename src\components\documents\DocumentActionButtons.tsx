import { isServer } from "solid-js/web";
import { SalesOrderData } from "~/interfaces/dto";
import { Printer } from "lucide-solid";

export default function DocumentActionButtons(props: {
	documentKey: string;
	salesOrder: SalesOrderData;
}) {
	const buttonClass =
		"text-white group-hover:text-yellow-400 transition-colors duration-200 ease-in-out hover:scale-110 mx-auto";

	function handlePrint() {
		if (isServer) return;
		window.print();
	}

	let formRef: HTMLFormElement | undefined;
	let dataFieldRef: HTMLInputElement | undefined;

	return (
		<form ref={formRef} method="post" target="_blank">
			<div class="fixed bottom-5 right-5 z-10 flex flex-col space-y-2 rounded-full bg-orange-800/75 px-4 py-4 print:hidden">
				<button type="button" class="group" onClick={handlePrint} title="Print">
					<Printer class={buttonClass} size={20} />
				</button>
			</div>
		</form>
	);
}
