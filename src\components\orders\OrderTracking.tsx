import { For, Show } from "solid-js";
import { SalesOrderData } from "~/interfaces/dto";
import { formatDate } from "~/utils/formatting-util";
import {
	LucideHourglass,
	LucidePaintbrush,
	LucidePackage,
	LucideTruck,
	LucideNotebookPen,
	LucideRows3,
	LucideMapPin,
} from "lucide-solid";

import shipIcon from "@assets/images/icons/ship-icon.png";
import { defaultPOLvalue } from "~/configs/app-config";

export default function OrderTracking(props: { salesOrder?: SalesOrderData }) {
	const steps = [
		{
			icon: LucideNotebookPen,
			text: "Pre-Production meeting",
			date: "",
			done: true,
		},
		{
			icon: LucideRows3,
			text: "Raw frame manufacture",
			date: "",
			done: true,
		},
		{ icon: LucideHourglass, text: "Sanding", date: "", done: true },
		{ icon: LucidePaintbrush, text: "Finishing", date: "", done: false },
		{ icon: LucidePackage, text: "Packing", date: "", done: false },
		{ icon: LucideTruck, text: "Ready to ship", date: "", done: false },
	];

	function getPOD() {
		const pod = props.salesOrder?.customFields?.custom3;
		return pod ? pod : "Semarang";
	}

	return (
		<div class="rounded-md border border-slate-300">
			<header class="flex justify-between rounded-tl-md rounded-tr-md bg-slate-100 px-4 py-4 text-sm leading-5 text-neutral-700">
				<div class="w-1/2">
					<ul>
						<li class="flex items-center">
							<div class="basis-1/3">Sales Order</div>
							<div>: {props.salesOrder?.orderNumber}</div>
						</li>
						<li class="flex items-center">
							<div class="basis-1/3">Customer Order/PO Number</div>
							<div>: {props.salesOrder?.poNumber}</div>
						</li>
					</ul>
				</div>
				<div class="w-1/2">
					<ul>
						<li class="flex items-center">
							<div class="basis-1/3">Order Date</div>
							<div>: {formatDate(props.salesOrder?.orderDate)}</div>
						</li>
						<li class="flex items-center">
							<div class="basis-1/3">Requested Ship Date</div>
							<div>: {formatDate(props.salesOrder?.requestedShipDate)}</div>
						</li>
						<li class="flex items-center">
							<div class="basis-1/3">Stuffing Date</div>
							<div>: {formatDate(props.salesOrder?.invoicedDate)}</div>
						</li>
					</ul>
				</div>
			</header>

			<div class="px-4 py-4">
				<div class="relative flex w-full flex-col items-center justify-between lg:flex-row lg:items-start">
					<For each={steps}>
						{(step, index) => (
							<div class="relative -left-1/4 w-full text-center lg:left-auto lg:mb-0 lg:w-1/6">
								<div class="relative mb-2 flex min-h-32 w-full flex-col items-center justify-center bg-white lg:min-h-min">
									<Show when={index() > 0}>
										<div
											class={`order-tracking-line absolute z-0 h-full w-0.5 lg:h-0.5 lg:w-full ${step.done ? "bg-green-700" : "bg-red-500"}`}
										></div>
									</Show>

									<div class="relative hidden lg:block">
										<step.icon size={24} class="text-lhBlue" />
									</div>

									<div
										class={`relative z-10 flex h-6 w-6 items-center justify-center rounded-full lg:mb-2 lg:mt-2 ${step.done ? "bg-green-700" : "bg-red-500"}`}
									>
										<div class="flex h-5 w-5 items-center justify-center rounded-full bg-white">
											<div
												class={`h-4 w-4 rounded-full ${step.done ? "bg-green-700" : "bg-red-500"}`}
											></div>
										</div>
									</div>

									<div class="absolute left-1/2 pl-6 text-left lg:relative lg:left-auto lg:pl-0 lg:text-center">
										<div class="relative z-10 w-full text-sm">{step.text}</div>

										<time class="relative mx-auto mt-1.5 block min-h-8 rounded-xs bg-slate-100 px-3 py-1.5 pt-2 text-xs text-neutral-500">
											<Show when={step.date} fallback="DD/MM/YYYY">
												{step.date}
											</Show>
										</time>
									</div>
								</div>
							</div>
						)}
					</For>
				</div>

				<div class="mt-7 pb-7 text-center text-neutral-700">
					<img src={shipIcon} alt="" class="mx-auto mb-5 max-h-24 w-auto" />

					<div class="mb-2 flex items-center justify-center">
						<time class="w-44 text-lg font-semibold text-lhBlue">
							28 August 2022
						</time>
						<div class="px-2 text-center font-bold text-lhBlue">
							<LucideMapPin class="mx-2 h-5 w-5" />
						</div>
						<time class="w-44 text-lg leading-5">
							POL
							<br />
							<span class="font-semibold text-lhBlue">{defaultPOLvalue}</span>
						</time>
					</div>

					<div class="mb-2 flex items-center justify-center">
						<time class="w-44 text-lg text-lhBlue">
							<strong class="font-semibold">32</strong> Days
						</time>
						<div class="px-2 text-center font-bold text-lhBlue">
							<LucideMapPin class="mx-2 h-5 w-5" />
						</div>
						<time class="w-44 text-lg leading-5">
							<small>Transhipment</small>
							<br />
							<span class="font-semibold text-lhBlue">Port KLANG</span>
						</time>
					</div>

					<div class="mb-2 flex items-center justify-center">
						<time class="w-44 text-lg font-semibold text-lhBlue">
							28 August 2022
						</time>
						<div class="px-2 text-center font-bold text-lhBlue">
							<LucideMapPin class="mx-2 h-5 w-5" />
						</div>
						<time class="w-44 leading-5">
							POD
							<br />
							<span class="text-lg font-semibold leading-5 text-lhBlue">
								{getPOD()}
							</span>
						</time>
					</div>
				</div>
			</div>
		</div>
	);
}
