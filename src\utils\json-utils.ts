export function maybeParseJson(
	value: string | Record<string, string> | undefined | null,
): Record<string, string> {
	let parsed: Record<string, string> = {};

	if (!value) {
		return parsed;
	}

	if (typeof value === "string") {
		try {
			parsed = JSON.parse(value);
		} catch (e) {
			console.error(
				"Error when parsing returned job order's custom column from Supabase:",
				e,
			);
		}
	} else {
		parsed = value;
	}

	return parsed;
}

export function jsonEncodeOrUndefined(value: unknown): string | undefined {
	if (value === undefined) {
		return undefined;
	}

	try {
		return JSON.stringify(value);
	} catch (e) {
		return undefined;
	}
}
