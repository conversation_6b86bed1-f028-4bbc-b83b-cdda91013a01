import customerServiceIcon from "@assets/images/icons/customer-service-icon.png";
import faq from "@assets/images/icons/faq.png";
import { BookUser, Settings } from "lucide-solid";

// interface CustomerServiceProps {
// 	name: string
	
// }


export default function CustomerService() {
	return (
		<div class="container items-center justify-center text-center">
			<img
				src={customerServiceIcon}
				alt=""
				class="mx-auto mb-2 mt-5 h-20 w-20"
			/>
			<h1 class="font-semibold text-xl">Wecome,</h1>
			<p class="text-lg">Legacy Home Customer Service</p>
			<div class="flex items-center justify-center mt-6">
				<img src={faq} alt="" class="mr-3 h-16 w-16" />
				<p class="text-lg text-black/70">Self Service articles frequently asked questions</p>
			</div>

			{/* input gede */}
			<form class="mt-5">
				<textarea
					class="h-40 w-1/2 rounded-md border border-solid border-lhGrey p-4"
					placeholder="Your message"
				></textarea>
			</form>
			<div class="mt-5 flex justify-center">
				<button class="rounded-md bg-gradient-to-t from-blue-800 to-blue-500 px-10 py-2 text-white">
					Send
				</button>
			</div>

			<button class="mt-5 flex items-center justify-start mx-auto border border-solid border-lhGrey rounded-md pl-2 py-2 w-1/5 bg-gradient-to-t from-gray-200 to-white">
				<div class="bg-gradient-to-t from-blue-800 to-blue-500 rounded-md p-2 text-center">
					<Settings size={30} color="white" />
				</div>
				<p class="ml-2">Asembly Instructions</p>
			</button>
			<button class="mt-5 flex items-center justify-start mx-auto border border-solid border-lhGrey rounded-md pl-2 py-2 w-1/5 bg-gradient-to-t from-gray-200 to-white">
				<div class="bg-gradient-to-t from-blue-800 to-blue-500 rounded-md p-2 text-center">
					<BookUser size={30} color="white" />
				</div>
				<p class="ml-2">General Care Instructions</p>
			</button>
		</div>
	);
}