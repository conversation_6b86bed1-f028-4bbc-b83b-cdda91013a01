import ky from "ky";
import { getErrorMessage, makeRequestHeaders } from "~/utils/http-util";
import { BaseResponse } from "~/interfaces/http";
import { AppConfig } from "~/configs/app-config";

export async function streamDocument(props: {
	customerId: string;
	salesOrderId: string;
	document: {
		key: string;
		text: string;
	};
	serveMethod: "download" | "stream";
}): Promise<BaseResponse> {
	const siteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = siteOpts.baseApiUrl;
	const apiUrl = `${baseApiUrl}/stream-document`;

	const supportedDocuments = AppConfig.supportedDocuments.map((doc) => doc.key);

	if (!supportedDocuments.includes(props.document.key)) {
		return {
			success: false,
			message: `Document name "${props.document.text}" is not ready yet. We're working hard to get it ready for you.`,
		};
	}

	try {
		const response = await ky.post(apiUrl, {
			credentials: "include",
			headers: makeRequestHeaders({
				"Content-Type": "application/json",
				Accept: "application/pdf, image/png, image/jpeg, application/json",
			}),
			json: {
				customer_id: props.customerId,
				sales_order_id: props.salesOrderId,
				document_name: props.document.key,
				serve_method: props.serveMethod,
			},
			timeout: false,
			retry: {
				limit: 1,
				methods: ["post"],
			},
		});

		// Determine response type based on Content-Type header
		const contentType = response.headers.get("Content-Type");

		if (!contentType) {
			return {
				success: false,
				message: "Response content type is empty",
			};
		}

		// Handle JSON response
		if (contentType.includes("application/json")) {
			const jsonResponse = await response.json<BaseResponse>();
			return jsonResponse;
		}

		if (
			!contentType.includes("application/pdf") &&
			!contentType.includes("image/")
		) {
			return {
				success: false,
				message: `Unsupported file type: ${String(contentType)}`,
			};
		}

		const blob = await response.blob();
		const contentDisposition = response.headers.get("Content-Disposition");
		let fileName = props.document.key; // fallback file name

		// Extract filename from Content-Disposition if present
		if (contentDisposition && contentDisposition.includes("filename=")) {
			const matches = contentDisposition.match(/filename="([^"]+)"/);
			if (matches && matches[1]) {
				fileName = matches[1];
			}
		}

		// Create blob URL
		const url = window.URL.createObjectURL(blob);

		// Handle PDF - display in browser or trigger download.
		if (contentType.includes("application/pdf")) {
			const anchor = document.createElement("a");
			anchor.href = url;

			if (props.serveMethod === "download") {
				// Set download attribute for downloading
				anchor.download = fileName;
			} else {
				// Set target="_blank" for opening in new tab
				anchor.target = "_blank";
			}

			// Add to DOM, trigger click, then remove
			document.body.appendChild(anchor);

			setTimeout(() => {
				anchor.click();
				anchor.remove();

				// Clean up the URL object after a delay to ensure the PDF has loaded
				setTimeout(() => {
					window.URL.revokeObjectURL(url);
				}, 5000);
			}, 100);

			// window.location.href = url;

			return {
				success: true,
				message:
					props.serveMethod === "download"
						? `Downloaded ${fileName}`
						: `Opened ${props.document.text} in new tab`,
			};
		}

		// Handle images (PNG/JPG) - trigger download.
		const anchor = document.createElement("a");
		anchor.href = url;
		anchor.download = fileName;

		// Add to DOM, trigger click, then remove
		document.body.appendChild(anchor);

		setTimeout(() => {
			anchor.click();
			anchor.remove();

			// Clean up the URL object after a delay to ensure the PDF has been loaded
			setTimeout(() => {
				window.URL.revokeObjectURL(url);
			}, 5000);
		}, 100);

		return {
			success: true,
			message: `Downloaded ${fileName}`,
		};
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
