import ky from "ky";
import {
	PricingSchemeListResponse,
	PricingSchemeResponse,
} from "~/interfaces/http";
import { getErrorMessage, makeRequestHeaders } from "~/utils/http-util";
import { ProductsFobPriceResponse } from "~/interfaces/response";
import { jsonEncodeOrUndefined } from "~/utils/json-utils";

export interface FetchPricingProps {
	includeCount?: boolean;
	count?: number;
	after?: string;
	before?: string;
	start?: string;
	skip?: number;
	sort?: string;
	sortDesc?: boolean;
	include?: string;
	keyword?: string;
	filters?: Record<string, any>;
}

export async function fetchPricingSchemes(
	props?: FetchPricingProps,
): Promise<PricingSchemeListResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const include = props?.include ?? "currency";

	let apiUrl = `${baseApiUrl}/pricing-schemes?include=${include}`;

	if (props?.after) {
		apiUrl += `&after=${props.after}`;
	}

	if (props?.before) {
		apiUrl += `&before=${props.before}`;
	}

	if (props?.start) {
		apiUrl += `&start=${props.start}`;
	}

	if (props?.skip) {
		apiUrl += `&skip=${props.skip}`;
	}

	if (props?.sort) {
		apiUrl += `&sort=${props.sort}`;
	}

	if (props?.sortDesc !== undefined) {
		apiUrl += `&sortDesc=${props.sortDesc}`;
	}

	if (props?.keyword) {
		apiUrl += `&filter[smart]=${props.keyword}`;
	}

	if (props?.filters) {
		// Encode to JSON.
		const filters = jsonEncodeOrUndefined(props.filters);

		if (filters) {
			apiUrl += `&filters=${filters}`;
		}
	}

	try {
		return await ky
			.get(apiUrl, {
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<PricingSchemeListResponse>();
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function findPricingScheme(
	id: string,
	include?: string,
): Promise<PricingSchemeResponse> {
	if (!id) {
		return {
			success: false,
			message: "Please provide pricing scheme id",
		};
	}

	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const apiUrl = `${baseApiUrl}/pricing-schemes/${id}?include=${include ?? "currency"}`;

	try {
		return await ky
			.get(apiUrl, {
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<PricingSchemeResponse>();
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function fetchProductsFobPriceByPricingSchemeId(
	pricingSchemeId: string,
): Promise<ProductsFobPriceResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const apiUrl = `${baseApiUrl}/page-data/pricing-scheme-fob-prices?pricingSchemeId=${pricingSchemeId}`;

	try {
		const response = await ky
			.get(apiUrl, {
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<ProductsFobPriceResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
