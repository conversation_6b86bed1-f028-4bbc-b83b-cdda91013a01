import { useParams } from "@solidjs/router";
import { createResource, Show } from "solid-js";
import LoadingSpinner from "~/components/components";
import BankAndAccountDetail from "~/components/documents/BankAndAccountDetail";
import InvoiceHeader from "~/components/documents/invoice-document/InvoiceHeader";
import InvoiceTable from "~/components/documents/invoice-document/InvoiceTable";
import { findInvoiceForm } from "~/services/document-preview-service";

async function fetchPageData() {
	const params = useParams();
	return await findInvoiceForm(params.id);
}

export default function InvoiceDocument() {
	const [invoiceFormResponse] = createResource(fetchPageData);

	return (
		<div class="w-[1280px] px-4 py-12 lg:container lg:w-auto print:w-auto print:px-0 print:py-0">
			<Show when={invoiceFormResponse.loading}>
				<LoadingSpinner />
			</Show>

			<Show
				when={invoiceFormResponse()?.success && invoiceFormResponse()?.data}
				fallback={<div>{invoiceFormResponse()?.message}</div>}
			>
				<InvoiceHeader
					salesOrder={invoiceFormResponse()?.data?.salesOrder}
					defaultPaymentTerm={
						invoiceFormResponse()?.data?.extraData?.defaultPaymentTerm
					}
				/>
				<InvoiceTable
					salesOrder={invoiceFormResponse()?.data?.salesOrder}
					customProductDataCollection={
						invoiceFormResponse()?.data?.extraData?.customProductDataCollection
					}
					defaultPaymentTerm={
						invoiceFormResponse()?.data?.extraData?.defaultPaymentTerm
					}
					incomeCategoryId={
						invoiceFormResponse()?.data?.extraData?.incomeCategoryId
					}
					options={invoiceFormResponse()?.data?.extraData}
				/>
			</Show>
			<BankAndAccountDetail />
		</div>
	);
}
