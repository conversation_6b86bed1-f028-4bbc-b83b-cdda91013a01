import { CustomProductData, SalesOrderData } from "~/interfaces/dto";
import {
	formatCurrency,
	formatDiscount,
	formatQuantity,
	formatWeight,
} from "~/utils/formatting-util";
import DocumentFootNote from "../DocumentFootNote";
import DocumentActionButtons from "../DocumentActionButtons";

import araParafImageSrc from "@assets/images/ara-paraf.png";

export default function SalesOrderTable(props: {
	salesOrder?: SalesOrderData;
	customProductDataCollection?: Record<string, CustomProductData>;
	incomeCategoryId?: string;
}) {
	if (!props.salesOrder) return null;

	let totalQuantity = 0;
	let totalHeight = 0;
	let totalWidth = 0;
	let totalLength = 0;
	let totalM3 = 0;
	let totalCBM = 0;

	const hasAnyCustomProductData =
		props.customProductDataCollection &&
		Object.keys(props.customProductDataCollection).length > 0;

	const productLines = props.salesOrder.lines ?? [];

	const hasAnyDiscount = productLines.some((line) => {
		const discount: number = Number(line.discount.value);

		if (discount > 0) {
			return true;
		}

		return false;
	});

	function formatPrice(amount: string | number): string {
		return formatCurrency({
			amount: amount,
			symbol: props.salesOrder?.currency?.symbol,
			thousandsSeparator: props.salesOrder?.currency?.thousandsSeparator,
			decimalSeparator: props.salesOrder?.currency?.decimalSeparator,
			decimalDigits: props.salesOrder?.currency?.decimalPlaces,
		});
	}

	const firstThSharedClass =
		"flex items-center flex-wrap justify-center border-x border-black p-1 font-medium";
	const thSharedClass =
		"flex items-center flex-wrap justify-center border-r border-black p-1 font-medium";

	const firstTdSharedClass = "border-x border-black p-1 print:p-0.5 xl:p-2";
	const tdSharedClass = "border-r border-black p-1 print:p-0.5 xl:p-2";

	// For customer download, the document should always be stamped.
	const isStamped = true;

	return (
		<div class="text-xs print:text-[10px]">
			<table class="mt-5 w-full table-fixed border-y border-black text-xs print:text-[10px]">
				<thead class="text-center">
					<tr class="flex w-full flex-wrap items-stretch border-b border-black bg-gray-100">
						<th class={`w-[21%] ${firstThSharedClass}`}>Multay Product</th>

						{hasAnyCustomProductData && (
							<th class={`w-[17%] ${thSharedClass}`}>Customer Product</th>
						)}

						<th
							class={`${
								hasAnyCustomProductData ? "w-[13%]" : "w-[30%]"
							} ${thSharedClass}`}
						>
							Picture *)
						</th>

						<th class={`${thSharedClass} w-[6%]`}>
							Quantity
							<br />
							(pcs)
						</th>
						<th class={`${thSharedClass} w-[5%]`}>H</th>
						<th class={`${thSharedClass} w-[5%]`}>W</th>
						<th class={`${thSharedClass} w-[5%]`}>D</th>
						<th class={`${thSharedClass} w-[5%]`}>M3</th>
						<th class={`${thSharedClass} w-[7%]`}>
							Total
							<br />
							M3
						</th>

						<th
							class={
								`${thSharedClass} ` + (hasAnyDiscount ? "w-[6%]" : " w-[7%]")
							}
						>
							Unit
							<br />
							Price (US $)
						</th>

						{hasAnyDiscount && <th class={`${thSharedClass} w-[4%]`}>Disc</th>}

						<th
							class={
								`${thSharedClass} ` + (hasAnyDiscount ? "w-[6%]" : " w-[9%]")
							}
						>
							Sub
							<br />
							Total (US $)
						</th>
					</tr>
				</thead>

				<tbody class="text-center">
					{props.salesOrder.lines?.map((line) => {
						const isIncomeProduct =
							line.product?.categoryId === props.incomeCategoryId;

						const quantity = line.quantity.standardQuantity;
						const height = line.product?.height;
						const discount = line.discount;
						const width = line.product?.length;
						const length = line.product?.width;
						const customFields = line.product?.customFields;
						const quantityNumber = Number(quantity);
						totalQuantity += isIncomeProduct ? 0 : quantityNumber;
						const unitPriceNumber = Number(line.unitPrice);

						const discountNumber = Number(discount.value);
						const discountAmount = discount.isPercent
							? unitPriceNumber * (discountNumber / 100)
							: discountNumber;
						const priceUnitAfterDiscount = unitPriceNumber - discountAmount;
						const priceAfterDiscount = priceUnitAfterDiscount * quantityNumber;

						// These are in cm unit.
						const heightNumber = Number(height);
						totalHeight += heightNumber;

						const widthNumber = Number(width);
						totalWidth += widthNumber;
						const lengthNumber = Number(length);
						totalLength += lengthNumber;

						const m3 = line.product?.customFields?.custom3 ?? "";
						const m3Number = Number(m3);

						const subTotalM3 = m3Number * quantityNumber;
						totalM3 += subTotalM3;
						const subTotal = unitPriceNumber * quantityNumber;

						const customProductData =
							props.customProductDataCollection?.[line.productId];

						return (
							<tr class="font-base flex w-full flex-wrap items-stretch border-b border-black text-gray-700">
								<td class={`${firstTdSharedClass} w-[21%] text-left`}>
									<span class="text-left uppercase">{line.product?.sku}</span>
									<br />
									<span class="text-left">{line.product?.name}</span>
								</td>

								{hasAnyCustomProductData && (
									<td class={`${tdSharedClass} w-[17%] text-left`}>
										{customProductData && (
											<>
												{customProductData.customerProductName && (
													<>
														<span>{customProductData.customerProductCode}</span>
														<br />
													</>
												)}
												<span>{customProductData.customerProductName}</span>
											</>
										)}
									</td>
								)}

								<td
									class={`${
										hasAnyCustomProductData ? "flex w-[13%]" : "flex w-[30%]"
									} ${tdSharedClass}`}
									colspan={customProductData ? undefined : 2}
								>
									{line.product &&
										line.product.images &&
										line.product.images.length > 0 && (
											<img
												src={line.product.images[0].largeUrl}
												alt="Product image"
												class="print:max-w-3/4 m-auto max-h-40 print:max-h-20"
											/>
										)}
								</td>

								<td class={`${tdSharedClass} w-[6%]`}>
									{!isIncomeProduct && formatQuantity(quantity)}
								</td>
								<td class={`${tdSharedClass} w-[5%]`}>
									{formatQuantity(height!)}
								</td>
								<td class={`${tdSharedClass} w-[5%]`}>
									{formatQuantity(width!)}
								</td>
								<td class={`${tdSharedClass} w-[5%]`}>
									{formatQuantity(length!)}
								</td>
								<td class={`${tdSharedClass} w-[5%]`}>{formatWeight(m3)}</td>
								<td class={`${tdSharedClass} w-[7%]`}>
									{formatWeight(subTotalM3)}
								</td>
								<td
									class={
										`${tdSharedClass} ` + (hasAnyDiscount ? "w-[6%]" : "w-[7%]")
									}
								>
									{formatPrice(line.unitPrice)}
								</td>

								{hasAnyDiscount && (
									<td class={`${tdSharedClass} w-[4%]`}>
										{discount.value !== "0.00000" && (
											<>
												{formatDiscount(discount.value ?? "")}
												{discount.isPercent && <span>%</span>}
											</>
										)}
									</td>
								)}

								<td
									class={
										`${tdSharedClass} ` + (hasAnyDiscount ? "w-[6%]" : "w-[9%]")
									}
								>
									<p>{formatPrice(priceAfterDiscount)}</p>
								</td>
							</tr>
						);
					})}
				</tbody>

				<tbody class="text-center">
					<tr class="flex w-full flex-wrap items-stretch">
						<td
							class={`${firstThSharedClass} w-[51%]`}
							colspan={hasAnyCustomProductData ? 3 : 2}
						>
							Total
						</td>
						<td class={`${thSharedClass} w-[6%]`}>{totalQuantity}</td>
						<td class={`${thSharedClass} w-[5%]`}></td>
						<td class={`${thSharedClass} w-[5%]`}>&nbsp;</td>
						<td class={`${thSharedClass} w-[5%]`}>&nbsp;</td>
						<td class={`${thSharedClass} w-[5%]`}>&nbsp;</td>
						<td class={`${thSharedClass} w-[7%]`}>{formatWeight(totalM3)}</td>
						<td
							class={
								`${thSharedClass} ` + (hasAnyDiscount ? "w-[6%]" : "w-[7%]")
							}
						></td>
						<td
							class={
								`${thSharedClass} ` + (hasAnyDiscount ? " w-[4%]" : "hidden")
							}
						></td>
						<td
							class={
								`${thSharedClass} ` + (hasAnyDiscount ? "w-[6%]" : "w-[9%]")
							}
						>
							{formatPrice(props.salesOrder.total)}
						</td>
					</tr>
				</tbody>
			</table>

			<DocumentFootNote />

			<section class="mt-4 flex w-full justify-between text-xs print:text-[9px] print:leading-[10px]">
				{props.salesOrder.orderRemarks && (
					<div class="w-8/12">
						<p class="font-medium">Remarks:</p>
						<div class="mt-1 w-4/6 border border-black p-1.5 print:w-5/6 print:p-0.5">
							<p class="text-gray-700" style="white-space: pre-line;">
								{props.salesOrder.orderRemarks}
							</p>
						</div>
					</div>
				)}

				{isStamped && (
					<div class="flex w-4/12 items-center justify-end">
						<div class="relative -right-3 flex items-center justify-start print:-right-8">
							<div>Signed: </div>
							<div class="relative ml-1 flex flex-col text-center">
								<div class="font-medium leading-[1]">Ara</div>
								<div class="relative -top-1">
									.................................
								</div>
							</div>
						</div>
						<img src={araParafImageSrc} class="w-52" alt="Multay Stamp" />
					</div>
				)}
			</section>

			<DocumentActionButtons
				documentKey="sales-order"
				salesOrder={props.salesOrder}
			/>
		</div>
	);
}
