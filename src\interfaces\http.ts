import {
	CategoryData,
	CustomProductData,
	CustomerData,
	CustomerPricingSchemeData,
	JobOrderCustomColumnData,
	JobOrderProductImagesData,
	JobOrderProductRemarksData,
	PaymentTermData,
	PricingSchemeData,
	ProductData,
	ProfileData,
	PurchaseOrderData,
	SalesOrderData,
	VendorData,
} from "./dto";

export interface BaseResponse {
	success: boolean;
	message: string;
	data?: unknown;
}

export interface SupabaseFetchResponse {
	status: number;
	statusText: string;
	data: Record<string, string | number>[];
}

export interface SupabaseInsertResponse {
	status: number;
	statusText: string;
	data: Record<string, string | number>[];
}

export interface JobOrderProductRemarkListResponse {
	success: boolean;
	message: string;
	data?: JobOrderProductRemarksData[];
}

export interface JobOrderCustomColumnListResponse {
	success: boolean;
	message: string;
	data?: JobOrderCustomColumnData[];
}
export interface JobOrderProductImagesListResponse {
	success: boolean;
	message: string;
	data?: JobOrderProductImagesData[];
}

export interface JobOrderProductRemarkResponse {
	success: boolean;
	message: string;
	data?: JobOrderProductRemarksData;
}

export interface JobOrderProductImageResponse {
	success: boolean;
	message: string;
	data?: JobOrderProductImagesData;
}

export interface JobOrderCustomColumnResponse {
	success: boolean;
	message: string;
	data?: JobOrderCustomColumnData;
}

export interface CustomProductDataResponse {
	success: boolean;
	message: string;
	data?: CustomProductData;
}

export interface CustomProductDataListResponse {
	success: boolean;
	message: string;
	data?: CustomProductData[];
}

export interface CreateCustomProductDataRespose {
	success: boolean;
	message: string;
	data?: CustomProductData;
}
export interface UpdateCustomProductDataRespose {
	success: boolean;
	message: string;
	data?: CustomProductData;
}

export interface DeleteCustomProductDataResponse {
	success: boolean;
	message: string;
}

export interface ProductListResponse {
	success: boolean;
	message: string;
	data?: ProductData[];
}

export interface ProductResponse {
	success: boolean;
	message: string;
	data?: ProductData;
}

export interface CategoryListResponse {
	success: boolean;
	message: string;
	data?: CategoryData[];
}

export interface CategoryResponse {
	success: boolean;
	message: string;
	data?: CategoryData;
}

export interface CustomerListResponse {
	success: boolean;
	message: string;
	data?: CustomerData[];
}

export interface CustomerResponse {
	success: boolean;
	message: string;
	data?: CustomerData;
}

export interface VendorListResponse {
	success: boolean;
	message: string;
	data?: VendorData[];
}

export interface VendorResponse {
	success: boolean;
	message: string;
	data?: VendorData;
}

export interface SalesOrderListResponse {
	success: boolean;
	message: string;
	data?: SalesOrderData[];
}

export interface SalesOrderResponse {
	success: boolean;
	message: string;
	data?: SalesOrderData;
}

export interface PaymentTermListResponse {
	success: boolean;
	message: string;
	data?: PaymentTermData[];
}

export interface PaymentTermResponse {
	success: boolean;
	message: string;
	data?: PaymentTermData;
}
export interface PriceListResponse {
	success: boolean;
	message: string;
	data?: PricingSchemeData[];
}

export interface PriceResponse {
	success: boolean;
	message: string;
	data?: PricingSchemeData;
}

export interface SendEmailResponse extends BaseResponse {}

export interface AuthDataResponse {
	success: boolean;
	message: string;
	data?: ProfileData;
}

export type CustomerPricingSchemeListResponse = {
	success: boolean;
	message: string;
	data?: CustomerPricingSchemeData[];
};

export type CustomerPricingSchemeResponse = {
	success: boolean;
	message: string;
	data?: CustomerPricingSchemeData;
};

export type UserListResponse = {
	success: boolean;
	message: string;
	data?: ProfileData[];
};

export type UserResponse = {
	success: boolean;
	message: string;
	data?: ProfileData;
};

export interface PurchaseOrderListResponse {
	success: boolean;
	message: string;
	data?: PurchaseOrderData[];
}

export interface PurchaseOrderResponse {
	success: boolean;
	message: string;
	data?: PurchaseOrderData;
}

export type PricingSchemeListResponse = {
	success: boolean;
	message: string;
	data?: PricingSchemeData[];
};

export type PricingSchemeResponse = {
	success: boolean;
	message: string;
	data?: PricingSchemeData;
};
