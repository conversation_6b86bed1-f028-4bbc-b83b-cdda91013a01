import { Search } from "lucide-solid";
import tiketsIcon from "../../assets/images/icons/tickets-icon.png";
import { For } from "solid-js";

interface imageProps {
	img: string;
}

function ImageItem(props: imageProps) {
	return (
		<div class="min-w-24 items-center justify-center">
			<img src={props.img} alt="" class="h-24 w-full border" />
			<button class="rounded-sm mt-2 border px-2 py-1 text-xs">Remarks</button>
		</div>
	);
}

export default function Tikets() {
	const exampleImages = [
		{
			img: "https://www.galleryfurnicraftjepara.com/wp-content/uploads/2023/02/FB_IMG_1676613545979-225x300.jpg",
		},
		{
			img: "https://www.galleryfurnicraftjepara.com/wp-content/uploads/2023/02/FB_IMG_1676613545979-225x300.jpg",
		},
		{
			img: "https://www.galleryfurnicraftjepara.com/wp-content/uploads/2023/02/FB_IMG_1676613545979-225x300.jpg",
		},
		{
			img: "https://www.galleryfurnicraftjepara.com/wp-content/uploads/2023/02/FB_IMG_1676613545979-225x300.jpg",
		},
		{
			img: "https://www.galleryfurnicraftjepara.com/wp-content/uploads/2023/02/FB_IMG_1676613545979-225x300.jpg",
		},
		{
			img: "https://www.galleryfurnicraftjepara.com/wp-content/uploads/2023/02/FB_IMG_1676613545979-225x300.jpg",
		},
	];

	return (
		<div class="container items-center justify-center text-center">
			<div class="container items-center justify-center text-center">
				<img src={tiketsIcon} alt="" class="mx-auto mt-5 mb-2 h-20 w-20" />
				<h1 class="text-xl font-semibold">Wecome,</h1>
				<p class="text-lg">Legacy Home Customer Service</p>
			</div>
			<div class="mt-5 flex items-center justify-between px-2 sm:px-0">
				<select class="rounded-sm border border-lhGrey border-solid px-4 py-1">
					<option>past year</option>
					<option>past month</option>
					<option>past week</option>
				</select>
				<div class="flex items-center justify-center">
					<div class="border-lhGrey flex items-center justify-center rounded-md border px-4 py-1">
						<Search size={20} color="black" />
						<input
							type="text"
							class="ml-2 w-20 rounded-md border border-none px-2 outline-hidden"
							placeholder="Search all orders"
						/>
					</div>
					<button class="ml-2 rounded-full bg-black px-4 py-2 text-xs text-white md:py-1.5 md:text-sm">
						Search <span class="hidden md:inline">Order</span>
					</button>
				</div>
			</div>
			<div class="mt-4 flex items-center justify-start px-2 sm:px-0">
				<h1>Ticket number</h1>
				<input
					type="text"
					class="rounded-sm border ml-2 w-40 border-black/30 bg-[#F1F5F9] px-2 py-1 outline-hidden"
				/>
			</div>
			<div class="grid items-center sm:px-0 lg:grid-cols-2 lg:gap-2">
				<div class="mt-2 border border-black">
					<div class="border-b border-black bg-[#F1F5F9] px-5 py-3 text-left">
						<div class="flex gap-3">
							<div>
								<p>Sales Order</p>
								<p>Customer Order / PO Number</p>
							</div>
							<div>
								<p>: Order Date</p>
								<p>: 2023-01-01</p>
							</div>
						</div>
					</div>
					<div class="justify-between gap-2 px-5 py-4 text-left text-sm sm:flex">
						<div>
							<div class="justify-betwen grid grid-cols-2 items-center">
								<p>Contain Number</p>
								<input
									type="text"
									class="rounded-sm border ml-2 max-w-40 border-black/30 px-2 py-1 outline-hidden"
								/>
							</div>
							<div class="justify-betwen grid grid-cols-2 items-center py-8">
								<p>Customer number</p>
								<input
									type="text"
									class="rounded-sm border ml-2 max-w-40 border-black/30 px-2 py-1 outline-hidden"
								/>
							</div>
							<div class="justify-betwen grid grid-cols-2 items-center">
								<p>Date of claim</p>
								<input
									type="text"
									class="rounded-sm border ml-2 max-w-40 border-black/30 px-2 py-1 outline-hidden"
								/>
							</div>
						</div>
						<div>
							<div class="justify-betwen grid grid-cols-2 items-center pt-8 sm:pt-0">
								<p>Quantity</p>
								<input
									type="text"
									class="rounded-sm border ml-2 max-w-40 border-black/30 px-2 py-1 outline-hidden"
								/>
							</div>
							<div class="justify-betwen grid grid-cols-2 items-center py-8">
								<p>Amount Claimed</p>
								<input
									type="text"
									class="rounded-sm border ml-2 max-w-40 border-black/30 px-2 py-1 outline-hidden"
								/>
							</div>
							<div class="flex justify-end">
								<select class="rounded-sm border border-solid border-black/20 bg-[#F1F5F9] px-4 py-1">
									<option>Resolution</option>
									<option>Replacement</option>
									<option>Credit Note</option>
									<option>Not Accepted</option>
								</select>
							</div>
						</div>
					</div>
				</div>
				<div class="grid gap-2 md:grid-cols-2">
					{/* hidden scroll bar */}
					<div class="max-w-96 border-black/30 px-5 py-4 md:border-r">
						<div class="scrollbar-none flex gap-4 overflow-x-auto">
							<For each={exampleImages}>
								{(image) => <ImageItem img={image.img} />}
							</For>
						</div>
						<div class="mt-2 text-left">
							<p class="underlie my-2 text-xs">Correspondence :</p>
							<textarea class="rounded-sm border h-24 w-full border-black/30 px-2 py-1 outline-hidden"></textarea>
						</div>
					</div>
					<div class="max-w-96 border-black/30 px-5 py-4">
						<div class="scrollbar-none flex gap-4 overflow-x-auto">
							<For each={exampleImages}>
								{(image) => <ImageItem img={image.img} />}
							</For>
						</div>
						<div class="mt-2 text-left">
							<p class="underlie my-2 text-xs">Solution :</p>
							<textarea class="rounded-sm border h-24 w-full border-black/30 px-2 py-1 outline-hidden"></textarea>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
