import LoadingSpinner, { HeaderSection } from "~/components/components";
import { createResource, createSignal, Show } from "solid-js";
import { findSalesOrder } from "~/services/sales-order-service";
import { useParams } from "@solidjs/router";

import orderTrackingIcon from "@assets/images/icons/order-tracking-icon.png";
import OrderTracking from "~/components/orders/OrderTracking";
import AlertDialog from "~/components/dialogs/AlertDialog";

async function fetchPageData() {
	const params = useParams();
	return await findSalesOrder(params.id);
}

export default function OrderTrackingPage() {
	const [salesOrderResponse] = createResource(fetchPageData);
	const user = window.legacyHomeSiteOpts.currentUser;

	const [isDialogOpen, setIsDialogOpen] = createSignal(true);

	return (
		<>
			<div class="orders-page pb-20">
				<div class="container relative">
					<HeaderSection
						iconUrl={orderTrackingIcon}
						title="Order Tracking"
						subtitle={salesOrderResponse()?.data?.orderNumber ?? ""}
					/>

					<div class="mx-auto mt-7">
						<Show when={salesOrderResponse.loading}>
							<LoadingSpinner />
						</Show>

						<Show when={salesOrderResponse.state === "ready"}>
							<OrderTracking salesOrder={salesOrderResponse()?.data} />
						</Show>
					</div>
				</div>
			</div>

			<AlertDialog
				isOpen={isDialogOpen()}
				title="Not Available"
				description="Order tracking is not yet available. We're working hard to get it ready for you."
				closeHandler={() => setIsDialogOpen(false)}
			/>
		</>
	);
}
