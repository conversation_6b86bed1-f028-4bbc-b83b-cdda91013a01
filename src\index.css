@import "tailwindcss";
@custom-variant dark (&:where(.dark, .dark *));

@font-face {
	font-family: "Myriad Pro";
	src:
		url("../assets/fonts/myriad-pro/myriad_pro_black-webfont.woff2")
			format("woff2"),
		url("../assets/fonts/myriad-pro/myriad_pro_black-webfont.woff")
			format("woff");
	font-weight: 900;
	font-style: normal;
}

@font-face {
	font-family: "Myriad Pro";
	src:
		url("../assets/fonts/myriad-pro/myriad_pro_bold-webfont.woff2")
			format("woff2"),
		url("../assets/fonts/myriad-pro/myriad_pro_bold-webfont.woff")
			format("woff");
	font-weight: 700;
	font-style: normal;
}

@font-face {
	font-family: "Myriad Pro";
	src:
		url("../assets/fonts/myriad-pro/myriad_pro_light-webfont.woff2")
			format("woff2"),
		url("../assets/fonts/myriad-pro/myriad_pro_light-webfont.woff")
			format("woff");
	font-weight: 300;
	font-style: normal;
}

@font-face {
	font-family: "Myriad Pro";
	src:
		url("../assets/fonts/myriad-pro/myriad_pro_regular-webfont.woff2")
			format("woff2"),
		url("../assets/fonts/myriad-pro/myriad_pro_regular-webfont.woff")
			format("woff");
	font-weight: normal;
	font-style: normal;
}

/* Start of TailwindCSS v4 config */
@layer base {
	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		border-color: var(--color-gray-200, currentColor);
	}

	input::placeholder,
	textarea::placeholder {
		color: var(--color-gray-400);
	}

	button:not(:disabled),
	[role="button"]:not(:disabled) {
		cursor: pointer;
	}

	dialog {
		margin: auto;
	}
}

@theme {
	--color-lhCompanyColor: #aa944a;
	--color-lhCompanyColorActive: #655317;
	--color-lightcolor: #f8fafc;
	--color-lightgreycolor: #e2e8f0;
	--color-lhBlack: #060721;
	--color-lhBlue: #1f427a;
	--color-lhBlueAlt: #087dc7;
	--color-lhLightBlue: #99c4ff;
	--color-docBlue: #435d89;
	--color-lhOrange: #dfc371;
	--color-lhGrey: #747170;
	--color-lhDangerColor: #ff6347;

	--box-shadow-custom: 0 0 9px rgba(0, 0, 0, 0.12);

	--font-sans: "Inter", sans-serif;
	--font-display: "Playfair Display", "Inter", sans-serif;
	--font-table: "Myriad Pro";
}

@utility container {
	margin-inline: auto;
	/* padding-inline: 2rem; */
}
/* End of TailwindCSS v4 config */

@media print {
	html,
	:host,
	body {
		font-family: "Arial", sans-serif !important;
	}

	table {
		page-break-after: auto;
	}

	tr {
		page-break-inside: avoid;
		page-break-after: auto;
	}

	td {
		page-break-inside: avoid;
		page-break-after: auto;
	}

	thead {
		display: table-header-group;
	}

	tfoot {
		display: table-footer-group;
	}
}

.offcanvas-overlay {
	background-color: rgba(0, 0, 0, 0.5);
	opacity: 0;
	visibility: hidden;
	transition:
		opacity 0.3s ease,
		visibility 0.3s ease;
}

.offcanvas-overlay.is-open {
	opacity: 1;
	visibility: visible;
	transition:
		opacity 0.3s ease,
		visibility 0.3s ease;
}

@media (min-width: 1024px) {
	.offcanvas-overlay {
		display: none;
	}

	.offcanvas-overlay.is-open {
		display: none;
	}
}

/* Start of menu bar */
.site-header {
	box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.14);
}

.menu-item > a {
	transition: color 0.2s;
}

.sub-menu {
	min-width: 200px;
	background-color: white;
	box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
	border-radius: 4px;
	opacity: 0;
	visibility: hidden;
	transform: scale(0.95);
	transform-origin: top left;
	transition: all 0.3s;
	z-index: 1;
}

.sub-menu li a {
	transition:
		background-color 0.2s,
		color 0.2s;
}

.sub-menu li a:hover {
	background-color: #f3f4f6;
	color: #2563eb;
}

.menu-item:hover .sub-menu {
	opacity: 1;
	visibility: visible;
	transform: scale(1);
}

.mobile-menu {
	width: 80%;
	max-width: 320px;
	box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
	transform: translateX(-100%);
	transition:
		transform 0.25s ease-in-out,
		opacity 0.5s ease-in-out,
		visibility 0.5s ease-in-out;
}

.mobile-menu.is-open {
	transform: translateX(0);
	visibility: visible;
	transition:
		transform 0.25s ease-in-out,
		opacity 0.25s ease-in-out,
		visibility 0.25s ease-in-out;
}

.order-tracking-line {
	top: calc(-60%);
}
/* End of menu bar */

@media screen and (min-width: 1024px) {
	.order-tracking-line {
		left: -50%;
		top: calc(50% - 18px);
		transform: translateY(-50%);
	}
}

@import "./ui.css";
