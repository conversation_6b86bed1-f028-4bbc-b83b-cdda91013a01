import { A, useParams } from "@solidjs/router";
import { For, Show } from "solid-js";
import { slugToUcWords } from "~/utils/formatting-util";

export default function Breadcrumb(props: { paths: string[] }) {
	const params = useParams();

	return (
		<ul class="breadcrumb flex items-center px-2 py-2.5 text-xs font-medium sm:text-sm xl:px-0">
			<For each={props.paths}>
				{(path, index) => {
					let url = "/";

					for (let i = 0; i <= index(); i++) {
						if (params.id && props.paths[i] === params.id) {
							continue;
						}

						url += `${props.paths[i]}/`;
					}

					if (params.id && props.paths[index()] === params.id) {
						return null;
					}

					return (
						<li class="flex">
							<Show when={index() > 0}>
								<span class="mx-1.5 text-lightgreycolor">/</span>
							</Show>

							<Show
								when={index() === props.paths.length - 1}
								fallback={
									<A
										href={url}
										class={`${
											index() === props.paths.length - 1
												? "text-lightgreycolor"
												: "text-lhOrange"
										} hover:text-lhOrange`}
									>
										{slugToUcWords(path)}
									</A>
								}
							>
								<span class="text-lightgreycolor">{slugToUcWords(path)}</span>
							</Show>
						</li>
					);
				}}
			</For>
		</ul>
	);
}
