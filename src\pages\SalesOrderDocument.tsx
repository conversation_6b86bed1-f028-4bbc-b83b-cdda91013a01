import { useParams } from "@solidjs/router";
import { createResource, Show } from "solid-js";
import BankAndAccountDetail from "~/components/documents/BankAndAccountDetail";
import LoadingSpinner from "../components/components";
import { findSalesOrderForm } from "~/services/document-preview-service";
import SalesOrderHeader from "../components/documents/sales-order-document/SalesOrderHeader";
import SalesOrderTable from "../components/documents/sales-order-document/SalesOrderTable";

async function fetchPageData() {
	const params = useParams();
	return await findSalesOrderForm(params.id);
}

export default function SalesOrderDocument() {
	const [salesOrderFormResponse] = createResource(fetchPageData);

	return (
		<div class="w-[1280px] px-4 py-12 lg:w-auto xl:px-20 print:w-auto print:px-0 print:py-0">
			<Show when={salesOrderFormResponse.loading}>
				<LoadingSpinner />
			</Show>

			<Show
				when={
					salesOrderFormResponse()?.success && salesOrderFormResponse()?.data
				}
				fallback={<div>{salesOrderFormResponse()?.message}</div>}
			>
				<SalesOrderHeader
					salesOrder={salesOrderFormResponse()?.data?.salesOrder}
				/>

				<SalesOrderTable
					salesOrder={salesOrderFormResponse()?.data?.salesOrder}
					customProductDataCollection={
						salesOrderFormResponse()?.data?.customProductDataCollection
					}
					incomeCategoryId={salesOrderFormResponse()?.data?.incomeCategoryId}
				/>
			</Show>
			<BankAndAccountDetail />
		</div>
	);
}
