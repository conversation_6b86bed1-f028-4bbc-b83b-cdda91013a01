import { Route, Router, RouteSectionProps, useLocation } from "@solidjs/router";
import {
	createEffect,
	createSignal,
	For,
	onCleanup,
	onMount,
	Show,
} from "solid-js";
import DashboardPage from "./pages/DashboardPage";
import OrdersPage from "./pages/OrdersPage";
import OrderTrackingPage from "./pages/OrderTrackingPage";
import DownloadDocumentsPage from "./pages/DownloadDocumentsPage";
import { AppConfig } from "./configs/app-config";
import { LucideMail, LucideMenu } from "lucide-solid";
import MobileMenu from "./components/menu/MobileMenu";
import Breadcrumb from "./components/Breadcrumb";
import AccountMenu from "./components/menu/AccountMenu";
import SalesOrderDocument from "./pages/SalesOrderDocument";
import Tikets from "./pages/Tikets";
import CustomerService from "./pages/CustomerService";
import YourStatement from "./pages/YourStatement";
import PackingListDocument from "./pages/PackingListDocument";
import InvoiceDocument from "./pages/InvoiceDocument";

import logoUrl from "@assets/images/legacy-home-indonesia-logo-horizontal.png";

const Layout = (props: RouteSectionProps) => {
	const siteOpts = window.legacyHomeSiteOpts;
	const currentUser = siteOpts.currentUser;

	const [currentLocation, setCurrentLocation] = createSignal(useLocation());
	const [mobileMenuOpen, setMobileMenuOpen] = createSignal(false);

	function onWindowResize() {
		if (window.innerWidth >= 1024) {
			setMobileMenuOpen(false);
		}
	}

	onMount(() => {
		window.addEventListener("resize", onWindowResize);
	});

	onCleanup(() => {
		window.removeEventListener("resize", onWindowResize);
	});

	createEffect(() => {
		const location = useLocation();
		setCurrentLocation(location);
	});

	const fullName = currentUser.displayName;

	// This `initialName` is the first letter of each user's display name.
	const initialName = fullName
		.split(" ")
		.map((word) => word[0])
		.join("")
		.toUpperCase();

	function getURLPaths() {
		const paths = currentLocation()
			.pathname.split("/")
			.filter((path, index) => {
				return path !== "";
			});

		if (paths.length === 1 && paths[0] === "dashboard") {
			paths.pop();
		}

		return paths;
	}

	function onMobileMenuButtonClick(e: Event) {
		e.preventDefault();
		setMobileMenuOpen(!mobileMenuOpen());
	}

	function closeMobileMenu(e?: Event) {
		setMobileMenuOpen(false);
	}

	function onSidebarMenuLinkClick(e?: Event) {
		if (window.innerWidth < 1024) {
			setMobileMenuOpen(false);
		}
	}

	/**
	 * This function checks if the current page has a header.
	 * Every page has a header except this: http://localhost:5000/dashboard/orders/{dynamic-sales-order-id}/{dynamic-document-name}
	 *
	 * @returns boolean
	 */
	function hasHeader(): boolean {
		const paths = currentLocation()?.pathname?.split("/").filter(Boolean) ?? [];

		return !(
			paths[0] === "dashboard" &&
			paths[1] === "orders" &&
			paths.length === 4 &&
			paths[3] !== "download-documents" &&
			paths[3] !== "order-tracking"
		);
	}

	return (
		<>
			<Show when={hasHeader()}>
				<MobileMenu isOpen={mobileMenuOpen()} closeHandler={closeMobileMenu} />

				<header class="site-header mb-12 bg-neutral-100">
					<div class="container flex h-20 items-center py-2">
						<div class="relative block h-full w-9/12 pl-2 lg:w-2/12 xl:pl-0">
							<a
								href={siteOpts.siteUrl}
								class="hover:text-lhBlue relative block h-full w-full"
							>
								<img
									src={logoUrl}
									alt="Legacy Home Indonesia"
									class="inline-block max-h-full"
								/>
							</a>
						</div>

						<nav class="main-nav flex h-full w-3/12 items-center justify-end pr-2 lg:w-10/12 xl:pr-0">
							<ul class="menu font-display text-lhGrey hidden h-full items-center lg:flex lg:text-sm xl:text-lg">
								<For each={AppConfig.mainMenu}>
									{(menuItem) => (
										<li class="menu-item relative flex h-full items-center">
											<a
												href={menuItem.url.replace(
													"{siteUrl}",
													siteOpts.siteUrl,
												)}
												class="hover:text-lhBlue relative block w-full px-4"
											>
												{menuItem.text}
											</a>
											<Show
												when={menuItem.children && menuItem.children.length > 0}
											>
												<ul class="sub-menu absolute top-full left-0 block text-base font-semibold">
													<For each={menuItem.children}>
														{(childMenuItem) => (
															<li class="sub-menu-item relative">
																<a
																	href={childMenuItem.url.replace(
																		"{siteUrl}",
																		siteOpts.siteUrl,
																	)}
																	class="relative block w-full px-4 py-2 font-medium"
																>
																	{childMenuItem.text}
																</a>
															</li>
														)}
													</For>
												</ul>
											</Show>
										</li>
									)}
								</For>
							</ul>

							<div class="flex h-full items-center justify-end pl-1">
								<a
									href={`${siteOpts.siteUrl}/contact/`}
									class="text-lhGrey hover:text-lhBlue relative hidden px-3 lg:inline-block"
								>
									<LucideMail />
								</a>

								<button
									type="button"
									class="mr-4 lg:hidden"
									onClick={onMobileMenuButtonClick}
								>
									<LucideMenu />
								</button>

								<AccountMenu />
							</div>
						</nav>
					</div>
					<div class="bg-lhBlue relative mb-1 py-2 sm:py-0">
						<div class="container flex min-h-8 flex-wrap items-center justify-between">
							<Show when={getURLPaths().length > 0}>
								<Breadcrumb paths={getURLPaths()} />
							</Show>
						</div>
					</div>
					<div class="bg-lhOrange h-1.5"></div>
				</header>
			</Show>

			<main>{props.children}</main>
		</>
	);
};

export default function App() {
	return (
		<Router root={Layout} explicitLinks={true}>
			<Route path="/" component={DashboardPage} />
			<Route path="/dashboard" component={DashboardPage} />
			<Route path="/dashboard/orders" component={OrdersPage} />
			<Route
				path="/dashboard/orders/:id/download-documents"
				component={DownloadDocumentsPage}
			/>
			<Route
				path="/dashboard/orders/:id/order-tracking"
				component={OrderTrackingPage}
			/>
			<Route path="/dashboard/tickets/" component={Tikets} />
			<Route path="/dashboard/customer-service/" component={CustomerService} />
			<Route path="/dashboard/statement/" component={YourStatement} />
			<Route
				path="/dashboard/orders/:id/sales-order"
				component={SalesOrderDocument}
			/>
			<Route
				path="/dashboard/orders/:id/packing-list"
				component={PackingListDocument}
			/>
			<Route path="/dashboard/orders/:id/invoice" component={InvoiceDocument} />
		</Router>
	);
}
