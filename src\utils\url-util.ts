import { AppUrlData } from "../interfaces/misc";

export function getUrlParts(urlObject: URL): string[] {
	return urlObject.pathname.split("/");
}

export function getImageUrl(path: string): string { 
  return new URL(path, import.meta.url).href;
};

export function getAppUrl(urlObject: URL): AppUrlData {
	const searchParams = urlObject.searchParams;
	const baseUrl = urlObject.origin;

	return {
		baseUrl: baseUrl,
		baseApiUrl: `${baseUrl}/api`,
		currentUrl: urlObject.href,
		keyword: searchParams.get("keyword"),
	};
}
