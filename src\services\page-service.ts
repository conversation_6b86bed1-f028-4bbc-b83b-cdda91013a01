import ky from "ky";
import { ProductData } from "~/interfaces/dto";
import { CustomerPriceListPageDataResponse } from "~/interfaces/response";
import {
	CollectionListResponse,
	CollectionResponse,
} from "~/interfaces/response";
import { getErrorMessage, makeRequestHeaders } from "~/utils/http-util";
import { fetchProducts } from "./product-service";

export async function getCustomerPriceListPageData(
	pricingSchemeSlug: string,
): Promise<CustomerPriceListPageDataResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const apiUrl = `${baseApiUrl}/page-data/customer-price-list/${pricingSchemeSlug}`;

	try {
		const response = await ky
			.get(apiUrl, {
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<CustomerPriceListPageDataResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function fetchCollections(): Promise<CollectionListResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const apiUrl = `${baseApiUrl}/page-data/collections`;

	try {
		const response = await ky
			.get(apiUrl, {
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<CollectionListResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function findCollection(
	categoryId: string,
): Promise<CollectionResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const apiUrl = `${baseApiUrl}/page-data/collections/${categoryId}`;

	try {
		const response = await ky
			.get(apiUrl, {
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<CollectionResponse>();

		return response;
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function fetchCollectionProducts(props: {
	existingProducts: ProductData[];
	perPage: number;
	collectionId: string;
	lastProductId?: string;
}): Promise<ProductData[]> {
	let products: ProductData[] = props.existingProducts;

	const productListResponse = await fetchProducts({
		count: props.perPage,
		after: props.lastProductId,
		isActive: true,
		categoryId: props.collectionId,
	});

	if (
		productListResponse.success &&
		productListResponse.data &&
		productListResponse.data.length > 0
	) {
		products.push(...productListResponse.data);

		if (productListResponse.data.length === props.perPage) {
			const lastProduct =
				productListResponse.data[productListResponse.data.length - 1];
			const lastProductId = lastProduct.productId;

			// Recursively fetch the next page of products.
			products = await fetchCollectionProducts({
				existingProducts: products,
				perPage: props.perPage,
				collectionId: props.collectionId,
				lastProductId: lastProductId,
			});
		}
	}

	return products;
}
