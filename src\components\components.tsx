import { A } from "@solidjs/router";
import { LucideLoaderCircle } from "lucide-solid";
import { Show } from "solid-js";

export default function LoadingSpinner(props: {
	class?: string;
	colorClass?: string;
	sizeClass?: string;
}) {
	return (
		<div
			class={`w-full text-center ${props.colorClass ?? "text-lhOrange"} ${
				props.class ?? ""
			}`}
		>
			<LucideLoaderCircle
				class={`mx-auto ${props.sizeClass ?? "h-10 w-10"} animate-spin`}
			/>
		</div>
	);
}

export function HeaderSection(props: {
	iconUrl: string;
	title: string;
	subtitle?: string;
}) {
	return (
		<div class="text-center">
			<img
				src={props.iconUrl}
				alt=""
				class="avatar mx-auto h-12 w-12 rounded-xl"
			/>

			<h1 class="text-lhGrey mt-1 text-lg font-light">{props.title}</h1>

			<Show when={props.subtitle}>
				<h3 class="text-md text-lhGrey">{props.subtitle}</h3>
			</Show>
		</div>
	);
}

export function OrderMenuLink(props: {
	href: string;
	iconUrl: string;
	text: string;
	class?: string;
}) {
	return (
		<A
			href={props.href}
			class={`border-lhGrey hover:bg-lightcolor flex h-10 items-center rounded-sm border border-solid bg-white px-2.5 text-sm text-slate-700 ${
				props.class ?? ""
			}`}
		>
			<img src={props.iconUrl} alt={props.text} class="mr-2 h-5 w-5" />
			{props.text}
		</A>
	);
}

export function DashboardMenuLink(props: {
	href: string;
	noMarginTop?: boolean;
	iconUrl: string;
	text: string;
}) {
	return (
		<A
			href={props.href}
			class={`${props.noMarginTop ? "" : "mt-3"} border-lhGrey hover:bg-lightcolor flex items-center rounded-sm border border-solid bg-white p-4`}
		>
			<img src={props.iconUrl} alt={props.text} class="mr-2 h-6 w-6" />
			{props.text}
		</A>
	);
}

export function AccountMenuLink(props: {
	href: string;
	noMarginTop?: boolean;
	iconUrl: string;
	text: string;
}) {
	return (
		<A
			href={props.href}
			class={`${props.noMarginTop ? "" : "mt-3"} border-lhGrey hover:bg-lightcolor flex items-center rounded-sm border bg-white px-4 py-1.5 text-black/70`}
		>
			<img src={props.iconUrl} alt={props.text} class="mr-2 h-6 w-6" />
			{props.text}
		</A>
	);
}

export function NotFound(props?: {
	image?: string;
	title?: string;
	message?: string;
}) {
	return (
		<div class="not-found container items-center justify-center py-7 text-center">
			<Show when={props?.image}>
				<img src={props?.image} alt="" class="mx-auto mt-5 mb-2 h-20 w-auto" />
			</Show>

			<h1 class="text-lhBlack mt-5 mb-3 text-4xl font-bold">
				{props?.title ?? "404"}
			</h1>
			<p class="text-lhGrey text-lg">{props?.message ?? "Page not found"}</p>
		</div>
	);
}
