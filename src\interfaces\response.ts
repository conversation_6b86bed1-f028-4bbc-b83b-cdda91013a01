import {
	CustomerPriceListPageData,
	InvoiceFormData,
	ProductCollectionData,
	SalesOrderFormData,
	StructuredCategoryData,
} from "./dto";

export interface BaseResponse {
	success: boolean;
	message: string;
	data?: any;
}

export interface LogOutResponse {
	success: boolean;
	message?: string;
}

export interface ProductCollectionResponse {
	success: boolean;
	message: string;
	data?: ProductCollectionData;
}

export interface ProductCollectionListResponse {
	success: boolean;
	message: string;
	data?: ProductCollectionData[];
}

export interface CollectionListResponse {
	success: boolean;
	message: string;
	data?: StructuredCategoryData[];
}

export interface CollectionResponse {
	success: boolean;
	message: string;
	data?: StructuredCategoryData;
}

export interface ProductsFobPriceResponse {
	success: boolean;
	message: string;
	data?: Record<string, string>;
}

export type CustomerPriceListPageDataResponse = {
	success: boolean;
	message: string;
	data?: CustomerPriceListPageData;
};

export type StreamDocumentResponse = {
	success: boolean;
	message: string;
	data?: {
		contentType: string;
		fileName: string;
	};
};

export type SalesOrderFormResponse = {
	success: boolean;
	message: string;
	data?: SalesOrderFormData;
};

export type InvoiceFormResponse = {
	success: boolean;
	message: string;
	data?: InvoiceFormData;
};
