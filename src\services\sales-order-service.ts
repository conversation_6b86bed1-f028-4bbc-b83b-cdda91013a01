import ky from "ky";
import { AppConfig } from "~/configs/app-config";
import { SalesOrderListResponse, SalesOrderResponse } from "~/interfaces/http";
import { getErrorMessage, makeRequestHeaders } from "~/utils/http-util";
import { jsonEncodeOrUndefined } from "~/utils/json-utils";

export async function fetchSalesOrders(props?: {
	includeCount?: boolean;
	count?: number;
	after?: string;
	before?: string;
	start?: string;
	skip?: number;
	sort?: string;
	sortDesc?: boolean;
	include?: string;
	keyword?: string;
	filters?: Record<string, any>;
}): Promise<SalesOrderListResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const includeCount = props?.includeCount ?? false;
	const count = props?.count ?? AppConfig.loadMorePerPage;
	const include = props?.include ?? "customer,salesRepTeamMember,currency";

	let apiUrl = `${baseApiUrl}/sales-orders?includeCount=${includeCount}&count=${count}&include=${include}`;

	if (props?.after) {
		apiUrl += `&after=${props.after}`;
	}

	if (props?.before) {
		apiUrl += `&before=${props.before}`;
	}

	if (props?.start) {
		apiUrl += `&start=${props.start}`;
	}

	if (props?.skip) {
		apiUrl += `&skip=${props.skip}`;
	}

	if (props?.sort) {
		apiUrl += `&sort=${props.sort}`;
	}

	if (props?.sortDesc) {
		apiUrl += `&sortDesc=${props.sortDesc}`;
	}

	if (props?.keyword) {
		apiUrl += `&keyword=${props.keyword}`;
	}

	if (props?.filters) {
		// Encode to JSON.
		const filters = jsonEncodeOrUndefined(props.filters);

		if (filters) {
			apiUrl += `&filters=${filters}`;
		}
	}

	try {
		return await ky
			.get(apiUrl, {
				credentials: "include",
				headers: makeRequestHeaders(),
				timeout: false,
				retry: {
					limit: 1,
					methods: ["get"],
				},
			})
			.json<SalesOrderListResponse>();
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function findSalesOrder(id: string): Promise<SalesOrderResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const include =
		"lines,lines.product,lines.product.images,customer,customer.defaultSalesRepTeamMember,salesRepTeamMember,paymentTerms,currency";

	const apiUrl = `${baseApiUrl}/sales-orders/${id}?include=${include}`;

	try {
		return await ky
			.get(apiUrl, {
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<SalesOrderResponse>();
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
