import { createSignal, For, Show } from "solid-js";
import {
	ChevronRight,
	ChevronDown,
	LayoutGrid,
	BookOpen,
	Brush,
	Box,
	Layers,
	MonitorPlay,
	LucideDot,
	LucideLogOut,
} from "lucide-solid";
import { AppConfig } from "~/configs/app-config";
import logoUrl from "@assets/images/legacy-home-indonesia-logo-horizontal.png";

export default function MobileMenu(props: {
	isOpen?: boolean;
	closeHandler?: () => void;
}) {
	const siteOpts = window.legacyHomeSiteOpts;
	const [expandedItem, setExpandedItem] = createSignal<string | undefined>();

	function getMenuIcon(text: string) {
		switch (text) {
			case "Dining Tables":
				return <LayoutGrid class="h-5 w-5" />;
			case "Collections":
				return <Box class="h-5 w-5" />;
			case "Lookbooks":
				return <BookOpen class="h-5 w-5" />;
			case "Finishing":
				return <Brush class="h-5 w-5" />;
			case "Fabrics & Leather":
				return <Layers class="h-5 w-5" />;
			case "Virtual Showroom":
				return <MonitorPlay class="h-5 w-5" />;
			default:
				return <ChevronRight class="h-5 w-5" />;
		}
	}

	return (
		<>
			<div
				class={`offcanvas-overlay fixed left-0 top-0 z-[9998] h-full w-full ${props.isOpen ? "is-open" : ""}`}
				onClick={props.closeHandler}
			/>
			<div
				class={`mobile-menu fixed inset-y-0 left-0 z-[9999] flex flex-col bg-white shadow-lg dark:bg-gray-900 ${props.isOpen ? "is-open" : ""}`}
			>
				<div class="border-b border-gray-200 p-6 dark:border-gray-800">
					<div class="flex items-center justify-center">
						<span class="text-xl font-semibold text-gray-800 dark:text-white">
							<img
								src={logoUrl}
								alt="Legacy Home Indonesia"
								class="inline-block max-h-full"
							/>
						</span>
					</div>
				</div>

				<nav class="flex-1 overflow-y-auto pt-4">
					<ul class="space-y-1 px-3 font-display">
						<For each={AppConfig.mainMenu}>
							{(item) => (
								<li>
									<div class="flex w-full items-center justify-between rounded-lg px-3 text-base text-gray-700 hover:bg-lightcolor dark:text-gray-200 dark:hover:bg-gray-800">
										<a
											href={item.url.replace("{siteUrl}", siteOpts.siteUrl)}
											class="flex flex-grow items-center py-3"
										>
											{getMenuIcon(item.text)}
											<span class="ml-3 font-medium">{item.text}</span>
										</a>
										<Show when={item.children && item.children.length > 0}>
											<button
												type="button"
												onClick={() =>
													setExpandedItem(
														expandedItem() === item.text
															? undefined
															: item.text,
													)
												}
											>
												<ChevronDown
													class={`h-5 w-5 transition-transform duration-200 ${
														expandedItem() === item.text
															? "rotate-180 transform"
															: ""
													}`}
												/>
											</button>
										</Show>
									</div>

									<Show
										when={
											item.children &&
											item.children.length > 0 &&
											expandedItem() === item.text
										}
									>
										<div class="ml-4 mt-1 space-y-1">
											<For each={item.children}>
												{(child) => (
													<a
														href={child.url}
														class="flex items-center rounded-lg px-3 py-2 text-sm text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800"
													>
														<LucideDot class="h-4 w-4" />
														<span class="ml-2">{child.text}</span>
													</a>
												)}
											</For>
										</div>
									</Show>
								</li>
							)}
						</For>

						<div class="flex w-full items-center justify-between rounded-lg px-3 text-base text-gray-700 hover:bg-lightcolor dark:text-gray-200 dark:hover:bg-gray-800">
							<a
								href={siteOpts.logoutUrl}
								class="flex flex-grow items-center py-3"
							>
								<LucideLogOut class="h-4 w-4" />
								<span class="ml-3 font-medium">Logout</span>
							</a>
						</div>
					</ul>
				</nav>

				<footer class="border-t border-gray-200 p-4 dark:border-gray-800">
					<div class="text-center text-xs text-gray-500 dark:text-gray-400">
						© 2025 Legacy Home Indonesia
					</div>
				</footer>
			</div>
		</>
	);
}
