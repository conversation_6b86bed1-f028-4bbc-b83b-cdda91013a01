import { JSXElement, children, onCleanup, onMount } from "solid-js";

export default function LoadMore(props: {
	onLoadMore: () => Promise<void>;
	scrollerRef?: HTMLElement | Window | undefined;
	contentRef?: HTMLElement | undefined;
	targetRef?: HTMLElement | undefined;
	distanceReducer?: number;
	children: JSXElement;
}) {
	let doingLoadMore = false;
	let loadMoreFinished = false;

	const distanceReducer = props.distanceReducer ?? 1;

	onMount(() => {
		const scrollerEl = props.scrollerRef ?? window;
		scrollerEl.addEventListener("scroll", handleOnScroll);
		handleOnScroll();
	});

	onCleanup(() => {
		const scrollerEl = props.scrollerRef ?? window;
		scrollerEl.removeEventListener("scroll", handleOnScroll);
	});

	function handleOnScroll(e?: Event) {
		if (!props.contentRef) return;

		const windowAsScroller = !props.scrollerRef || props.scrollerRef === window;

		if (windowAsScroller) {
			checkWindowScroll();
			return;
		}

		checkNonWindowScroll(props.scrollerRef as HTMLElement);
	}

	function checkWindowScroll() {
		const footerEl = document.querySelector(".site-footer");
		if (!footerEl) return;

		const footerTop = footerEl.getBoundingClientRect().top;

		if (footerTop > window.innerHeight) {
			return;
		}

		loadMore();
	}

	function checkNonWindowScroll(scroller: HTMLElement) {
		if (
			scroller.scrollTop + scroller.clientHeight >=
			scroller.scrollHeight - 2
		) {
			loadMore();
		}
	}

	async function loadMore() {
		if (doingLoadMore || loadMoreFinished) return;

		doingLoadMore = true;
		await props.onLoadMore();
		doingLoadMore = false;
	}

	const childComponent = children(() => {
		return props.children;
	});

	return <>{childComponent()}</>;
}
