import { For } from "solid-js";
import { SalesOrderData } from "~/interfaces/dto";
import { formatCurrency, formatDate } from "~/utils/formatting-util";

export function QuoteListTable(props: { quotes: SalesOrderData[] }) {
	return (
		<>
			<table>
				<thead>
					<tr>
						<th>Quote Number</th>
						<th>Customer</th>
						<th>Date</th>
						<th>Deposit Status</th>
						<th>Total Order</th>
					</tr>
				</thead>
				<tbody>
					<For each={props.quotes}>
						{(salesOrder) => {
							function formatPrice(amount: string | number): string {
								return formatCurrency({
									amount: amount,
									symbol: salesOrder.currency?.symbol,
									thousandsSeparator: salesOrder.currency?.thousandsSeparator,
									decimalSeparator: salesOrder.currency?.decimalSeparator,
									decimalDigits: salesOrder.currency?.decimalPlaces,
								});
							}

							let depositStatus = "Deposit paid";

							const paid = parseFloat(salesOrder.amountPaid);

							if (isNaN(paid) || paid <= 0) {
								depositStatus = "No deposit";
							}

							return (
								<tr>
									<td>{salesOrder.orderNumber}</td>
									<td>{salesOrder.customer?.name}</td>
									<td>{formatDate(salesOrder.orderDate)}</td>
									<td>{depositStatus}</td>
									<td>{formatPrice(salesOrder.total)}</td>
								</tr>
							);
						}}
					</For>
				</tbody>
			</table>
		</>
	);
}
