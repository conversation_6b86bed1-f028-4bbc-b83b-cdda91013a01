import ky from "ky";
import { UserListResponse, UserResponse } from "~/interfaces/http";
import { MetaQuery } from "~/interfaces/misc";
import { getErrorMessage, makeRequestHeaders } from "~/utils/http-util";
import { jsonEncodeOrUndefined } from "~/utils/json-utils";

export async function fetchUsers(props?: {
	apiUrl?: string;
	limit?: number;
	offset?: number;
	role?: string;
	roleIn?: string[];
	roleNotIn?: string[];
	include?: number[];
	exclude?: number[];
	search?: string;
	searchColumns?: string[];
	orderBy?: string;
	order?: string;
	metaKey?: string;
	metaValue?: unknown;
	metaCompare?: string;
	metaQuery?: MetaQuery;
}): Promise<UserListResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const apiUrl = props?.apiUrl ?? `${baseApiUrl}/users`;
	const params = new URLSearchParams();

	if (props?.limit) {
		const limit = props.limit < 0 ? -1 : props.limit;
		params.append("limit", limit.toString());
	}

	if (props?.offset || props?.offset === 0) {
		params.append("offset", props.offset.toString());
	}

	if (props?.role) {
		params.append("role", props.role);
	}

	if (props?.roleIn) {
		params.append("role__in", props.roleIn.join(","));
	}

	if (props?.roleNotIn) {
		params.append("role__not_in", props.roleNotIn.join(","));
	}

	if (props?.include) {
		params.append("include", props.include.join(","));
	}

	if (props?.exclude) {
		params.append("exclude", props.exclude.join(","));
	}

	if (props?.search) {
		params.append("search", props.search);
	}

	if (props?.searchColumns) {
		params.append("search_columns", props.searchColumns.join(","));
	}

	if (props?.orderBy) {
		params.append("order_by", props.orderBy);
	}

	if (props?.order) {
		params.append("order", props.order);
	}

	if (props?.metaKey) {
		params.append("meta_key", props.metaKey);
	}

	if (props?.metaValue) {
		params.append("meta_value", String(props.metaValue));
	}

	if (props?.metaCompare) {
		params.append("meta_compare", props.metaCompare);
	}

	if (props?.metaQuery) {
		const encodedMetaQuery = jsonEncodeOrUndefined(props.metaQuery);

		if (encodedMetaQuery) {
			params.append("meta_query", encodedMetaQuery);
		}
	}

	try {
		return await ky
			.get(apiUrl, {
				searchParams: params,
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<UserListResponse>();
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}

export async function fetchSubscribers(props?: {
	limit?: number;
	offset?: number;
	include?: number[];
	exclude?: number[];
	search?: string;
	searchColumns?: string[];
	orderBy?: string;
	order?: string;
	metaKey?: string;
	metaValue?: unknown;
	metaCompare?: string;
	metaQuery?: MetaQuery;
}): Promise<UserListResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	const apiUrl = `${baseApiUrl}/subscribers`;

	return await fetchUsers({
		apiUrl: apiUrl,
		roleIn: ["subscriber"],
		limit: props?.limit,
		offset: props?.offset,
		include: props?.include,
		exclude: props?.exclude,
		search: props?.search,
		searchColumns: props?.searchColumns,
		orderBy: props?.orderBy,
		order: props?.order,
		metaKey: props?.metaKey,
		metaValue: props?.metaValue,
		metaCompare: props?.metaCompare,
		metaQuery: props?.metaQuery,
	});
}

export async function fetchCustomers(props?: {
	limit?: number;
	offset?: number;
	include?: number[];
	exclude?: number[];
	search?: string;
	searchColumns?: string[];
	orderBy?: string;
	order?: string;
	metaKey?: string;
	metaValue?: unknown;
	metaCompare?: string;
	metaQuery?: MetaQuery;
}): Promise<UserListResponse> {
	const baseApiUrl = window.legacyHomeSiteOpts.baseApiUrl;

	const apiUrl = `${baseApiUrl}/customers`;

	return await fetchUsers({
		apiUrl: apiUrl,
		roleIn: ["multay_customer"],
		limit: props?.limit,
		offset: props?.offset,
		include: props?.include,
		exclude: props?.exclude,
		search: props?.search,
		searchColumns: props?.searchColumns,
		orderBy: props?.orderBy,
		order: props?.order,
		metaKey: props?.metaKey,
		metaValue: props?.metaValue,
		metaCompare: props?.metaCompare,
		metaQuery: props?.metaQuery,
	});
}

export async function findUser(id: number): Promise<UserResponse> {
	const legacyHomeSiteOpts = window.legacyHomeSiteOpts;
	const baseApiUrl = legacyHomeSiteOpts.baseApiUrl;

	try {
		return await ky
			.get(`${baseApiUrl}/users/${id}`, {
				credentials: "include",
				headers: makeRequestHeaders(),
			})
			.json<UserResponse>();
	} catch (e: unknown) {
		return {
			success: false,
			message: getErrorMessage(e),
		};
	}
}
