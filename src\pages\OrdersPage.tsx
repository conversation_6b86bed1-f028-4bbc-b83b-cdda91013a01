import LoadingSpinner, { HeaderSection } from "~/components/components";

import ordersIcon from "@assets/images/icons/orders-icon.png";
import { UserData } from "~/interfaces/dto";
import { createResource, createSignal, For, Show, Suspense } from "solid-js";
import { LucideSearch } from "lucide-solid";
import { fetchSalesOrders } from "~/services/sales-order-service";
import OrderListWithLoadMore from "~/components/orders/OrderListWithLoadMore";
import { useSearchParams } from "@solidjs/router";
import { ucFirst } from "~/utils/formatting-util";

export default function OrdersPage() {
	const user: UserData = window.legacyHomeSiteOpts.currentUser;
	const [searchParams] = useSearchParams();

	async function fetchPageData() {
		const filters: Record<string, any> = {};

		filters.customerId = window.legacyHomeSiteOpts.currentUser.inflowCustomerId;

		filters.isActive = true;

		if (searchParams.keyword) {
			filters.orderNumber = searchParams.keyword;
		}

		if (searchParams.year) {
			filters.orderDate = {
				fromDate: `${searchParams.year}-01-01`,
				toDate: `${searchParams.year}-12-31`,
			};
		}

		return await fetchSalesOrders({
			filters: filters,
			sortDesc: true,
		});
	}

	const [salesOrdersResponse] = createResource(fetchPageData);

	const currentYear = new Date().getFullYear();
	const years = Array.from({ length: 7 }, (_, i) => currentYear - i);

	let searchFieldRef: HTMLInputElement | undefined;
	let yearSelectFieldRef: HTMLSelectElement | undefined;

	function isYearSelected(year: number | string | undefined) {
		const yearFromParams = Array.isArray(searchParams.year)
			? searchParams.year[0]
			: searchParams.year;

		if (!year) {
			return !yearFromParams ? true : false;
		}

		return String(year) === yearFromParams;
	}

	function getKeywordAsInputValue() {
		const keyword = Array.isArray(searchParams.keyword)
			? searchParams.keyword[0]
			: searchParams.keyword;

		return keyword ?? "";
	}

	const [activeTab, setActiveTab] = createSignal<"orders" | "quotes">("orders");

	function handleTabSwitch(key: "orders" | "quotes") {
		setActiveTab(key);
	}

	return (
		<div class="orders-page pb-20">
			<div class="relative container">
				<HeaderSection
					iconUrl={ordersIcon}
					title={`Welcome, ${user.displayName}`}
					subtitle={`Your ${ucFirst(activeTab())}`}
				/>

				<div class="filters-area mx-auto mt-7 mb-7 flex flex-wrap items-center justify-between px-4 text-sm sm:px-0 xl:max-w-[80%]">
					<div class="mb-4 flex w-full items-center justify-start sm:mb-0 sm:w-1/2">
						<div class="mr-2 text-slate-500">Order date:</div>

						<select
							ref={yearSelectFieldRef}
							class="h-9 rounded-sm border border-solid border-slate-300 bg-slate-100 px-4 font-medium"
						>
							<option value="" selected={isYearSelected(undefined)}>
								All years
							</option>
							<For each={years}>
								{(year) => {
									return (
										<option value={year} selected={isYearSelected(year)}>
											{year}
										</option>
									);
								}}
							</For>
						</select>
					</div>

					<div class="flex w-full items-center justify-end sm:w-1/2">
						<div class="search-wrapper relative flex h-9 w-full items-center md:w-[350px]">
							<input
								ref={searchFieldRef}
								type="search"
								class="relative h-full w-full rounded-sm border border-solid border-slate-300 bg-slate-100 pr-4 pl-9 font-medium"
								placeholder="Search by sales order number"
								value={getKeywordAsInputValue()}
							/>
							<LucideSearch class="absolute left-2 h-5 w-5" />
						</div>
					</div>
				</div>

				<div class="data-list mx-auto px-4 sm:px-0 xl:max-w-[80%]">
					<Show when={salesOrdersResponse.loading}>
						<LoadingSpinner />
					</Show>

					<Show when={salesOrdersResponse.state === "ready"}>
						<OrderListWithLoadMore
							customerId={user.inflowCustomerId ?? ""}
							salesOrders={salesOrdersResponse()?.data}
							searchField={searchFieldRef}
							yearSelectField={yearSelectFieldRef}
							onTabSwitch={handleTabSwitch}
						/>
					</Show>
				</div>
			</div>
		</div>
	);
}
