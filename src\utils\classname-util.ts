export function getInputFieldClassName({
	submissionStatus,
}: {
	submissionStatus: string;
}) {
	return `mt-1.5 block w-full rounded-md border border-gray-400 p-3 outline-hidden ${
		submissionStatus === "error" || submissionStatus === "failed"
			? "border-red-500"
			: ""
	}`;
}

export const mainTagClassName =
	"mx-auto mt-10 max-w-md px-[1rem] text-center text-xs font-semibold";

export const iconVisibilityClassName = "h-4 w-4 fill-gray-500";

export const h3TagClassName = "text-xl font-bold";

export const linkIconClassName = "h-5 w-5 group-hover:fill-white";

export const pTagClassName = "mt-2 text-gray-500";
